import pytest

from joyce_svc import di
from joyce_svc.config import settings


@pytest.fixture(autouse=True)
def _api_rate_limit_reset(monkeypatch):
    """
    For API tests, avoid cross-test interference from the global in-proc rate limiter.
    - Set a very high limit
    - Clear the DI cache so a fresh limiter is created for each test
    """
    monkeypatch.setattr(settings, "RATE_LIMIT_PER_MIN", 1_000_000)
    if hasattr(di.get_rate_limiter, "cache_clear"):
        di.get_rate_limiter.cache_clear()
    yield
    # Clear again after the test to avoid bleed-over to non-API tests
    if hasattr(di.get_rate_limiter, "cache_clear"):
        di.get_rate_limiter.cache_clear()
