from __future__ import annotations

from joyce_svc.ports.metrics import MetricsPort


class NoopMetrics(MetricsPort):
    def inc_auth_failures(self) -> None:
        return None

    def inc_rate_limit_hits(self) -> None:
        return None

    def observe_request_duration_ms(self, *, path: str, method: str, status_code: int, elapsed_ms: float) -> None:
        return None

    def observe_node_duration_ms(self, *, node: str, elapsed_ms: float) -> None:
        return None

    def set_gauge(self, *, name: str, value: float, labels: dict | None = None) -> None:
        return None
