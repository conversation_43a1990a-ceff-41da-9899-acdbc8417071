from __future__ import annotations

import json
import time
from typing import Any, Dict, Optional, Tuple

import httpx
import jwt
from fastapi import Request
from jwt import PyJWK, PyJWKSet
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from joyce_svc.config import settings
from joyce_svc.logging import get_logger

logger = get_logger(__name__)


class JWKSResolver:
    """
    Resolve signing keys from JWKS via URL or local file path.
    Caches result in-memory for a short TTL to avoid frequent I/O.
    """

    def __init__(self, jwks_url: Optional[str], jwks_path: Optional[str], ttl_sec: int = 300) -> None:
        self.jwks_url = jwks_url
        self.jwks_path = jwks_path
        self.ttl_sec = ttl_sec
        self._cache: Optional[Tuple[float, PyJWKSet]] = None

    async def _load_jwks(self) -> PyJWKSet:
        now = time.time()
        if self._cache and now - self._cache[0] < self.ttl_sec:
            return self._cache[1]

        if self.jwks_path:
            with open(self.jwks_path, "r", encoding="utf-8") as f:
                data = json.load(f)
        elif self.jwks_url:
            async with httpx.AsyncClient(timeout=5.0) as client:
                resp = await client.get(self.jwks_url)
                resp.raise_for_status()
                data = resp.json()
        else:
            raise RuntimeError("JWKS not configured")

        jwks = PyJWKSet.from_dict(data)
        self._cache = (now, jwks)
        return jwks

    async def get_key_for_kid(self, kid: str) -> Optional[PyJWK]:
        jwks = await self._load_jwks()
        # PyJWKSet does not expose dict by kid directly across versions; search linearly
        for k in jwks.keys:
            if getattr(k, "key_id", None) == kid:
                return k
        return None


class JWTVerifier:
    """
    Verifies Bearer JWT using JWKS, issuer and audience constraints.
    """

    def __init__(self) -> None:
        # Resolver is constructed per verification to honor dynamic test-time settings updates.
        pass

    async def verify_from_request(self, request: Request) -> Dict[str, Any]:
        auth = request.headers.get("authorization") or request.headers.get("Authorization")
        if not auth or not auth.lower().startswith("bearer "):
            raise jwt.InvalidTokenError("Missing Bearer token")

        token = auth.split(" ", 1)[1].strip()
        return await self.verify(token)

    async def verify(self, token: str) -> Dict[str, Any]:
        try:
            unverified_header = jwt.get_unverified_header(token)
        except jwt.JWTError as e:
            raise jwt.InvalidTokenError(f"Invalid token header: {e}") from e

        kid = unverified_header.get("kid")
        if not kid:
            raise jwt.InvalidTokenError("Missing kid in JWT header")

        # Build resolver using current settings at verification time (supports tests that mutate settings).
        resolver = JWKSResolver(settings.OIDC_JWKS_URL, settings.OIDC_JWKS_PATH)
        jwk = await resolver.get_key_for_kid(kid)
        if not jwk:
            raise jwt.InvalidTokenError("Signing key not found for kid")

        key = jwk.key
        # Strict options; verify signature, exp, nbf, iat, aud, iss
        options = {"require": ["exp", "iat"], "verify_signature": True}
        try:
            claims = jwt.decode(
                token,
                key=key,
                algorithms=[unverified_header.get("alg", "RS256")],
                audience=settings.OIDC_AUDIENCE,
                issuer=settings.OIDC_ISSUER,
                options=options,
            )
        except jwt.ExpiredSignatureError as e:
            raise e
        except jwt.InvalidAudienceError as e:
            raise e
        except jwt.InvalidIssuerError as e:
            raise e
        except jwt.PyJWTError as e:
            raise jwt.InvalidTokenError(str(e)) from e

        return claims


class AuthMiddleware(BaseHTTPMiddleware):
    """
    JWT verification middleware.

    Behavior:
    - If AUTH_REQUIRED is False, skip verification (dev only) but mark auth as skipped.
    - Else attempt verification on every request.
      * On success: store claims to request.state.jwt_claims
      * On failure:
          - For /v1/chat: do NOT return here; store error to request.state.auth_error (chat route will stream SSE error)
          - For other routes: return 401 JSON immediately.

    This ensures streaming endpoints can emit a stable error frame, while others reject early.
    """

    def __init__(self, app) -> None:
        super().__init__(app)
        self.verifier = JWTVerifier()

    async def dispatch(self, request: Request, call_next):
        # Bypass auth for health and docs endpoints
        path = request.url.path
        if path.startswith("/health") or path.startswith("/docs") or path.startswith("/openapi.json"):
            return await call_next(request)

        if not settings.AUTH_REQUIRED:
            request.state.jwt_claims = None
            request.state.auth_skipped = True
            return await call_next(request)

        try:
            claims = await self.verifier.verify_from_request(request)
            request.state.jwt_claims = claims
            request.state.auth_skipped = False
            # Extract common fields for downstream use
            request.state.user_id = claims.get("sub") or claims.get("user_id")
            request.state.user_role = claims.get("role") or claims.get("app_role")
            request.state.tenant_from_jwt = claims.get("tenantId") or claims.get("tenant") or claims.get("tid")
            return await call_next(request)
        except Exception as e:
            # Log auth failure (avoid reserved 'message' key in logging extras)
            logger.warning("JWT verification failed", extra={"path": path, "error": str(e)})
            if path.startswith("/v1/chat"):
                request.state.auth_error = e
                return await call_next(request)
            return JSONResponse(
                status_code=401,
                content={"detail": "Unauthorized"},
            )
