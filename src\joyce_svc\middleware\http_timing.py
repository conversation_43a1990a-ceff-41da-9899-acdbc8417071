from __future__ import annotations

import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from joyce_svc.logging import get_logger
from joyce_svc.di import get_metrics

logger = get_logger(__name__)


class HttpTimingMiddleware(BaseHTTPMiddleware):
    """
    Measures per-request elapsed time and logs a structured entry.
    Also forwards the observation to the MetricsPort (noop by default).
    """

    async def dispatch(self, request: Request, call_next: Callable):
        t0 = time.time()
        try:
            response: Response = await call_next(request)
            return response
        finally:
            elapsed_ms = (time.time() - t0) * 1000.0
            path = request.url.path
            method = request.method
            status_code = getattr(request, "state", None) and getattr(getattr(request, "state"), "status_code", None)
            # status_code is not set on request.state; get from response if available
            # note: response may not be accessible here if an exception occurred before creation
            try:
                status_code = response.status_code  # type: ignore[name-defined]
            except Exception:
                status_code = status_code or 500

            # Log structured line
            try:
                logger.info(
                    "http_timing",
                    extra={
                        "path": path,
                        "method": method,
                        "status_code": status_code,
                        "elapsed_ms": int(elapsed_ms),
                    },
                )
            except Exception:
                pass

            # Record metrics (noop by default)
            try:
                metrics = get_metrics()
                metrics.observe_request_duration_ms(path=path, method=method, status_code=int(status_code), elapsed_ms=elapsed_ms)
            except Exception:
                pass
