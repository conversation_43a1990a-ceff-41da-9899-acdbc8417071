from __future__ import annotations

from typing import Dict, List


# Declarative registry of flow step sequences.
# Keep names aligned with node function registrations in graph.py.
FLOWS: Dict[str, List[str]] = {
    "investor_snapshot": ["fetch_mirror_data", "fetch_sei_data", "fetch_alerts_data", "structure_response"],
    "margin_driver": ["analyze_margin", "structure_response"],
    "execution_proposal": ["fetch_sei_data", "structure_response"],
    "fallback": ["clarify_question", "structure_response"],
}


def get_flows() -> Dict[str, List[str]]:
    # Return a shallow copy to avoid mutation by callers
    return dict(FLOWS)


def first_step_for(label: str) -> str:
    steps = FLOWS.get(label)
    if not steps or len(steps) == 0:
        raise KeyError(f"No steps registered for flow '{label}'")
    return steps[0]


def linear_edges() -> List[tuple[str, str]]:
    """
    Compute linear edges for all flows from the registry.
    """
    edges: List[tuple[str, str]] = []
    for steps in FLOWS.values():
        if len(steps) < 2:
            continue
        for i in range(len(steps) - 1):
            a, b = steps[i], steps[i + 1]
            edges.append((a, b))
    # Deduplicate edges while preserving order
    unique: List[tuple[str, str]] = []
    seen: set[tuple[str, str]] = set()
    for e in edges:
        if e not in seen:
            unique.append(e)
            seen.add(e)
    return unique
