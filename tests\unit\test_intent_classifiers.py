"""Unit tests for intent classifiers."""

import pytest
from unittest.mock import Mock, patch
import asyncio

from joyce_svc.adapters.llm.rule_based import RuleBasedClassifier
from joyce_svc.adapters.llm.ensemble import EnsembleClassifier
from joyce_svc.ports.llm import IntentClassifier


def test_rule_based_classifier_basic_intents():
    """Test rule-based classifier for basic intent patterns."""
    clf = RuleBasedClassifier()
    
    # Investor snapshot variations
    assert clf.classify("Give me an investor brief")["intent"] == "investor_snapshot"
    assert clf.classify("5-bullet summary please")["intent"] == "investor_snapshot"
    assert clf.classify("I need a 5 bullet brief")["intent"] == "investor_snapshot"
    
    # Margin driver variations
    assert clf.classify("What's driving the margin change?")["intent"] == "margin_driver"
    assert clf.classify("whats driving gross margin")["intent"] == "margin_driver"
    assert clf.classify("Show me margin drivers")["intent"] == "margin_driver"
    
    # Execution proposal variations
    assert clf.classify("Propose execution plan")["intent"] == "execution_proposal"
    assert clf.classify("Who are the owners and timelines?")["intent"] == "execution_proposal"
    assert clf.classify("I need a proposal with owners")["intent"] == "execution_proposal"


def test_rule_based_classifier_edge_cases():
    """Test rule-based classifier with ambiguous or unclear inputs."""
    clf = RuleBasedClassifier()
    
    # Ambiguous - no clear intent
    assert clf.classify("hello")["intent"] is None
    assert clf.classify("what can you do?")["intent"] is None
    assert clf.classify("help me understand the data")["intent"] is None
    
    # Mixed signals (should pick first match)
    result = clf.classify("investor brief with margin drivers")
    assert result["intent"] in ["investor_snapshot", "margin_driver"]
    
    # Typos and variations
    # "brief" still matches investor snapshot per current rules
    assert clf.classify("invstr brief")["intent"] == "investor_snapshot"
    # This one should not match margin/driver exact cues
    assert clf.classify("margn drivrs")["intent"] is None  # Too much typo


def test_rule_based_classifier_confidence():
    """Test that rule-based classifier returns confidence scores."""
    clf = RuleBasedClassifier()
    
    result = clf.classify("Give me an investor brief")
    assert "confidence" in result
    assert 0 <= result["confidence"] <= 1
    
    # High confidence for clear matches
    clear_result = clf.classify("5-bullet investor brief")
    assert clear_result["confidence"] >= 0.8
    
    # Low/no confidence for unclear
    unclear_result = clf.classify("random text here")
    assert unclear_result["intent"] is None
    assert unclear_result["confidence"] == 0


def test_ensemble_classifier_fallback_chain():
    """Test ensemble classifier fallback from primary to secondary."""
    # Mock classifiers
    primary = Mock(spec=IntentClassifier)
    secondary = Mock(spec=IntentClassifier)
    
    ensemble = EnsembleClassifier(primary=primary, fallback=secondary)
    
    # Case 1: Primary succeeds
    primary.classify.return_value = {"intent": "investor_snapshot", "confidence": 0.9}
    result = ensemble.classify("test query")
    assert result["intent"] == "investor_snapshot"
    primary.classify.assert_called_once_with("test query")
    secondary.classify.assert_not_called()
    
    # Case 2: Primary returns None, fallback to secondary
    primary.classify.reset_mock()
    secondary.classify.reset_mock()
    primary.classify.return_value = {"intent": None, "confidence": 0}
    secondary.classify.return_value = {"intent": "margin_driver", "confidence": 0.7}
    
    result = ensemble.classify("ambiguous query")
    assert result["intent"] == "margin_driver"
    primary.classify.assert_called_once()
    secondary.classify.assert_called_once()
    
    # Case 3: Both return None
    primary.classify.return_value = {"intent": None, "confidence": 0}
    secondary.classify.return_value = {"intent": None, "confidence": 0}
    
    result = ensemble.classify("unclear query")
    assert result["intent"] is None


def test_ensemble_classifier_exception_handling():
    """Test ensemble classifier handles exceptions gracefully."""
    primary = Mock(spec=IntentClassifier)
    secondary = Mock(spec=IntentClassifier)
    
    ensemble = EnsembleClassifier(primary=primary, fallback=secondary)
    
    # Primary throws exception, ensemble should attempt fallback
    primary.classify.side_effect = Exception("Primary failed")
    secondary.classify.return_value = {"intent": "execution_proposal", "confidence": 0.8}
    
    result = ensemble.classify("test")
    assert result.get("intent") in {"execution_proposal", None}
    
    # Both throw exceptions
    primary.classify.side_effect = Exception("Primary failed")
    secondary.classify.side_effect = Exception("Secondary failed")
    
    result = ensemble.classify("test")
    assert result["intent"] is None
    assert result["confidence"] == 0


def test_anthropic_classifier_timeout_simulation():
    """Simulate timeout behavior for Anthropic classifier."""
    # This is a conceptual test - actual implementation would need mocking
    # of the Anthropic API client
    
    class MockAnthropicClassifier:
        def __init__(self, timeout_ms: int):
            self.timeout_ms = timeout_ms
        
        async def classify_async(self, text: str):
            # Simulate a slow API call
            await asyncio.sleep(self.timeout_ms / 1000 * 2)  # Exceed timeout
            return {"intent": "investor_snapshot", "confidence": 0.95}
        
        def classify(self, text: str):
            try:
                # Simulate timeout enforcement
                loop = asyncio.new_event_loop()
                task = loop.create_task(self.classify_async(text))
                result = loop.run_until_complete(
                    asyncio.wait_for(task, timeout=self.timeout_ms / 1000)
                )
                return result
            except asyncio.TimeoutError:
                return {"intent": None, "confidence": 0, "error": "timeout"}
            finally:
                loop.close()
    
    # Test with short timeout
    clf = MockAnthropicClassifier(timeout_ms=100)
    result = clf.classify("test query")
    assert result.get("error") == "timeout"
    assert result["intent"] is None
