# Joyce Service (joyce-svc) - FastAPI + SSE
# Minimal, production-ready image with clean linter posture
FROM python:3.13-alpine3.22

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_NO_CACHE_DIR=on \
    PYTHONPATH=/app/src \
    PIP_ROOT_USER_ACTION=ignore

WORKDIR /app

# Install build dependencies and CA certificates
# These are commonly needed for Python packages that compile C extensions
RUN apk add --no-cache \
    gcc \
    musl-dev \
    libffi-dev \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Create non-root user (Alpine syntax)
RUN addgroup -S app && adduser -S app -G app

# Install Python deps
COPY requirements.txt /app/requirements.txt
RUN pip install --upgrade pip && pip install --no-cache-dir -r /app/requirements.txt

# Copy application
COPY --chown=app:app src /app/src
COPY --chown=app:app docker/entrypoint.sh /app/docker/entrypoint.sh

# Create data directory for volume mounting
RUN mkdir -p /data && chown -R app:app /data

RUN chmod +x /app/docker/entrypoint.sh && chown -R app:app /app
USER app:app

# Runtime env defaults
# Note: Do not bake sensitive or security-affecting env into the image.
# AUTH_REQUIRED defaults to True in the app (see src/joyce_svc/config.py). Override at runtime if needed.
ENV PORT=8080 \
    LOG_LEVEL=info \
    ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,https://app-dev.rejoyce.ai,https://app-stg.rejoyce.ai,https://app.rejoyce.ai

EXPOSE 8080

# Use exec-form ENTRYPOINT for proper signal handling and to avoid shell-form warnings
ENTRYPOINT ["/app/docker/entrypoint.sh"]