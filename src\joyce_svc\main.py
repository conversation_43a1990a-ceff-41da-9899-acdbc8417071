from __future__ import annotations

from fastapi import <PERSON><PERSON><PERSON>
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware

from joyce_svc.config import settings
from joyce_svc.logging import get_logger, setup_logging
from joyce_svc.middleware.auth import AuthMiddleware
from joyce_svc.middleware.correlation import CorrelationIdMiddleware
from joyce_svc.middleware.rate_limit import RateLimitMiddleware
from joyce_svc.middleware.http_timing import HttpTimingMiddleware
from joyce_svc.routes.health import router as health_router
from joyce_svc.routes.chat import router as chat_router

# Initialize structured logging early
setup_logging(settings.LOG_LEVEL)
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("joyce-svc startup")
    try:
        yield
    finally:
        # Shutdown
        logger.info("joyce-svc shutdown")


def create_app() -> FastAPI:
    app = FastAPI(
        title="Joyce Service (joyce-svc)",
        version="0.1.0",
        docs_url="/docs",
        redoc_url=None,
        lifespan=lifespan,
    )

    # CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins_list,
        allow_origin_regex=settings.ALLOWED_ORIGIN_REGEX,
        allow_credentials=False,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["X-Correlation-Id"],
    )

    # Middlewares (order matters).
    # NOTE: Starlette executes the last-added middleware FIRST on requests (outermost).
    # We add in reverse so runtime execution order is:
    #   Correlation -> HttpTiming -> Auth -> RateLimit -> route
    app.add_middleware(RateLimitMiddleware)       # executes LAST among these on requests
    app.add_middleware(AuthMiddleware)            # executes before RateLimit (populates request.state.*)
    app.add_middleware(HttpTimingMiddleware)      # executes before Auth to capture full timing
    app.add_middleware(CorrelationIdMiddleware)   # executes FIRST to set correlation id

    # Routers
    app.include_router(health_router)
    app.include_router(chat_router, prefix="/v1")


    return app


app = create_app()
