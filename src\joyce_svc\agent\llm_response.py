from __future__ import annotations

import json
from typing import Any, Dict, Iterable, List, Optional

from joyce_svc.config import settings
from joyce_svc.logging import get_logger
from joyce_svc.models.schemas import Action, SourceRef

logger = get_logger(__name__)

_SECTION_HEADERS = [
    "Acknowledgment:",
    "Validation:",
    "Key insights:",
    "Evidence:",
    "Risks:",
    "Next steps:",
]


def _safe_json(value: Any) -> str:
    try:
        return json.dumps(value, ensure_ascii=False, default=str)
    except Exception:
        return str(value)


def _sources_to_text(sources: Iterable[SourceRef]) -> str:
    items: List[str] = []
    for src in sources or []:
        label = getattr(src, "docId", None)
        title = getattr(src, "title", None)
        if not label:
            continue
        if title:
            items.append(f"{label}: {title}")
        else:
            items.append(label)
    return "; ".join(items) if items else ""


def _actions_to_text(actions: Iterable[Action]) -> str:
    outputs: List[str] = []
    for action in actions or []:
        label = getattr(action, "label", None)
        href = getattr(action, "href", None)
        if not label:
            continue
        if href:
            outputs.append(f"{label} → {href}")
        else:
            outputs.append(str(label))
    return "; ".join(outputs)


def _call_openai_responder(system_prompt: str, user_prompt: str) -> Optional[str]:
    from langchain_openai import ChatOpenAI  # type: ignore
    from langchain_core.messages import HumanMessage, SystemMessage  # type: ignore

    timeout = max(0.1, settings.AGENT_RESPONDER_TIMEOUT_MS / 1000.0)
    model = ChatOpenAI(
        model=settings.AGENT_RESPONDER_MODEL,
        temperature=0.2,
        timeout=timeout,
        max_retries=1,
    )
    response = model.invoke(
        [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt),
        ]
    )
    content = getattr(response, "content", None)
    if isinstance(content, str):
        return content
    if isinstance(content, list):
        return "\n".join(str(part) for part in content)
    return str(response)


def _call_claude_responder(system_prompt: str, user_prompt: str) -> Optional[str]:
    import os
    from anthropic import Anthropic  # type: ignore

    api_key = settings.ANTHROPIC_API_KEY or os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        raise ValueError("ANTHROPIC_API_KEY not provided for responder model")

    timeout = max(0.1, settings.AGENT_RESPONDER_TIMEOUT_MS / 1000.0)
    client = Anthropic(api_key=api_key, max_retries=1)
    response = client.messages.create(
        model=settings.AGENT_RESPONDER_MODEL,
        max_tokens=900,
        temperature=0.2,
        timeout=timeout,
        system=system_prompt,
        messages=[{"role": "user", "content": user_prompt}],
    )
    if response.content:
        return response.content[0].text
    return None


def _invoke_llm(system_prompt: str, user_prompt: str) -> Optional[str]:
    model_name = (settings.AGENT_RESPONDER_MODEL or "").lower()
    if "claude" in model_name:
        return _call_claude_responder(system_prompt, user_prompt)
    return _call_openai_responder(system_prompt, user_prompt)


def _has_all_sections(text: str) -> bool:
    lowered = text or ""
    return all(header.split(":")[0].lower() in lowered.lower() for header in _SECTION_HEADERS)


def generate_llm_consultant_message(
    *,
    draft: str,
    intent: Optional[str],
    ctx: Dict[str, Any],
    sources: List[SourceRef],
    actions: List[Action],
) -> Optional[str]:
    """
    Use an LLM to craft the final consultant-style response. Returns None when the
    LLM path is disabled or fails, signalling the caller to use deterministic fallback.
    """

    if not settings.AGENT_LLM_RESPONDER_ENABLED:
        return None

    system_prompt = (
        "You are Joyce, a senior consultant. Compose executive-ready responses using "
        "six sections with the exact labels: Acknowledgment, Validation, Key insights, "
        "Evidence, Risks, Next steps. Keep answers concise and grounded in provided data."
    )

    ctx_summary = {
        "intent": intent,
        "uiContext": ctx.get("uiContext"),
        "planner": ctx.get("planning"),
        "analysis": ctx.get("analysis"),
        "mirror": (ctx.get("mirror") or {}).get("kpi"),
    }
    payload = {
        "draft": draft,
        "citations": _sources_to_text(sources),
        "actions": _actions_to_text(actions),
        "context": ctx_summary,
    }
    user_prompt = (
        "Craft the final response following the consultant persona.\n"
        "Information bundle (JSON): {info}\n"
        "Rules:\n"
        "- Ask a humble validation question.\n"
        "- Cite sources using docId labels inside the Evidence section.\n"
        "- If citations are empty, state that data may be incomplete.\n"
        "- If actions are provided, reference them in Next steps.\n"
        "- Preserve critical facts from the draft, but you can rephrase for clarity."
    ).format(info=_safe_json(payload))

    try:
        text = _invoke_llm(system_prompt, user_prompt)
    except Exception:
        logger.debug("LLM responder invocation failed", exc_info=True)
        return None

    if not text:
        return None
    if not _has_all_sections(text):
        # When the LLM does not follow instructions, fall back to deterministic formatter.
        logger.debug("LLM responder output missing required sections", extra={"output": text})
        return None
    return text.strip()
