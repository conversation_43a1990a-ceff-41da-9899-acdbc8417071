from __future__ import annotations

from typing import Optional

from anthropic import Anthropic

from joyce_svc.ports.llm import IntentClassifier, ClassificationResult


class AnthropicClassifier(IntentClassifier):
    """
    Anthropic-backed intent classifier.
    Returns a normalized ClassificationResult with best-effort parsing.
    """

    def __init__(self, *, model: str, timeout_s: float = 0.6) -> None:
        # The Anthropic client reads API key from env ANTHROPIC_API_KEY
        self._client = Anthropic(timeout=timeout_s)
        self._model = model

    def classify(self, text: str) -> ClassificationResult:
        try:
            system = (
                "Classify the user's query into exactly one of these intents: "
                "investor_snapshot, margin_driver, execution_proposal. "
                "Respond with just the intent token, no explanations."
            )
            resp = self._client.messages.create(
                model=self._model,
                max_tokens=8,
                system=system,
                messages=[{"role": "user", "content": text}],
            )
            label = ""
            try:
                if getattr(resp, "content", None):
                    for block in resp.content:
                        if getattr(block, "type", None) == "text" and getattr(block, "text", None):
                            label = block.text
                            break
                    if not label and resp.content:
                        label = str(resp.content[0])
            except Exception:
                label = ""

            norm = (label or "").strip().lower().replace("-", "_").replace(" ", "_")
            allowed = {"investor_snapshot", "margin_driver", "execution_proposal"}
            if norm in allowed:
                # Confidence is heuristic; LLM single-token classification treated as medium confidence.
                return ClassificationResult(intent=norm, confidence=0.6)  # type: ignore[typeddict-item]
        except Exception:
            # Fail safe
            pass
        return ClassificationResult(intent=None, confidence=0.0)
