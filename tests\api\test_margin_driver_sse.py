"""API tests for margin driver SSE end-to-end behavior."""

import json
from starlette.testclient import TestClient

from joyce_svc.main import app
from joyce_svc.config import settings
from joyce_svc import di
from joyce_svc.evidence.doc_registry import DOC_MIRROR_TRENDS, DOC_ALERTS


class _FakeTools:
    """Deterministic tools for SSE tests."""
    def fetch_kpi_trends(self, *, company_symbol: str, kpi_name: str):
        from joyce_svc.ports.tools import KPITrendModel
        return KPITrendModel(
            kpi=kpi_name,
            points=[
                {"date": "2025-06", "value": 41.2},
                {"date": "2025-07", "value": 41.8},
                {"date": "2025-08", "value": 42.1},
            ],
        )

    def fetch_executive_summary(self, *, company_symbol: str):
        from joyce_svc.ports.tools import ExecutiveSummaryModel
        return ExecutiveSummaryModel(
            highlights=["Q3 revenue up 5%"],
            risks=["Supply chain pressures"],
            cta="Review detailed metrics",
        )

    def fetch_alerts_summary(self, *, company_symbol: str):
        from joyce_svc.ports.tools import AlertsSummaryModel
        return AlertsSummaryModel(
            categories=["margin", "operations"],
            top_alerts=["Margin compression in Region A", "Inventory buildup"],
        )

    def fetch_ebitda_margin(self, *, company_symbol: str):
        from joyce_svc.ports.tools import EBITDAMarginModel
        return EBITDAMarginModel(
            current_margin=42.1, previous_margin=41.8, trend="up", period="Q3 2025"
        )

    def fetch_value_leakage(self, *, company_symbol: str):
        from joyce_svc.ports.tools import ValueLeakageModel
        return ValueLeakageModel(
            areas=["Procurement inefficiencies"],
            estimated_impact="1.5% margin opportunity",
            mitigation_strategies=["Renegotiate contracts", "Tighten vendor funding controls"],
            priority="high",
        )


def _collect_sse_frames(resp):
    frames = []
    for line in resp.iter_lines():
        if line and line.startswith("data: "):
            frame_data = json.loads(line.replace("data: ", ""))
            frames.append(frame_data)
    return frames


def test_margin_driver_sse_final_contains_expected_fields(monkeypatch):
    """Final SSE frame for margin driver should include structured content, actions, and sources."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    monkeypatch.setattr(di, "get_tools", lambda: _FakeTools())

    client = TestClient(app)

    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-200",
        "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin"},
        "message": "What's driving the gross margin change?",
        "response_format": "advisor_v1",
    }

    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        frames = _collect_sse_frames(resp)

    assert len(frames) >= 3
    assert frames[0]["type"] == "start"

    final_frames = [f for f in frames if f["type"] == "final"]
    assert len(final_frames) == 1
    final = final_frames[0]

    # Content assertions
    assert "content" in final and isinstance(final["content"], str)
    assert "EBITDA margin" in final["content"]
    assert "Top drivers:" in final["content"]
    assert "Next actions:" in final["content"]

    # Actions present and deep link correct
    assert "actions" in final and isinstance(final["actions"], list)
    assert len(final["actions"]) >= 1
    first = final["actions"][0]
    # action may already be dict shape from Pydantic
    assert first.get("label") == "Open Digital Mirror (EBITDA margin)"
    assert first.get("href") == "/digital-mirror?kpi=ebitda-margin"

    # Sources present (can be empty, but we expect mirror or alerts for this flow)
    assert "sources" in final and isinstance(final["sources"], list)
    doc_ids = {s.get("docId") for s in final["sources"] if isinstance(s, dict)}
    # At least one expected doc id should be present
    assert (DOC_MIRROR_TRENDS in doc_ids) or (DOC_ALERTS in doc_ids)

    # Correlation id present
    assert "meta" in final and "correlationId" in final["meta"]


def test_margin_driver_sse_tool_frames_when_enabled(monkeypatch):
    """When STREAM_FROM_GRAPH=true, tool frames should appear even for margin driver requests."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    monkeypatch.setattr(settings, "STREAM_FROM_GRAPH", True)
    monkeypatch.setattr(di, "get_tools", lambda: _FakeTools())

    client = TestClient(app)

    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-201",
        "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin"},
        "message": "What's driving the gross margin change?",
        "response_format": "advisor_v1",
    }

    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        frames = _collect_sse_frames(resp)

    tool_frames = [f for f in frames if f["type"] == "tool"]
    assert len(tool_frames) > 0
    for tf in tool_frames:
        assert "content" in tf
        assert "graph" in tf["content"].lower() or "agent" in tf["content"].lower()
