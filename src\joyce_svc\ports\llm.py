from __future__ import annotations

from typing import Optional, Protocol, TypedDict, Literal


# Keep Intent type aligned with agent.types.Intent
Intent = Literal["investor_snapshot", "margin_driver", "execution_proposal"]


class ClassificationResult(TypedDict, total=False):
    intent: Optional[Intent]
    confidence: Optional[float]  # 0.0 - 1.0


class IntentClassifier(Protocol):
    """
    Port for intent classification.
    Implementations may be rule-based, LLM-based, or an ensemble of both.
    """

    def classify(self, text: str) -> ClassificationResult: ...
