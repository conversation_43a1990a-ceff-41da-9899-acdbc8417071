from __future__ import annotations

from functools import lru_cache
from typing import Optional
from pathlib import Path

from joyce_svc.config import settings
from joyce_svc.adapters.tools.file_backed import FileBackedToolsAdapter
from joyce_svc.ports.tools import ToolsPort
from joyce_svc.ports.metrics import MetricsPort
from joyce_svc.ports.memory import MemoryPort
from joyce_svc.ports.llm import IntentClassifier
from joyce_svc.ports.llm import Intent as LLMIntent  # re-export type alias

# Metrics adapter (noop)
from joyce_svc.adapters.metrics.noop import NoopMetrics

# Memory adapter (in-proc)
from joyce_svc.adapters.memory.inproc import InprocMemoryStore

# LLM classifiers
from joyce_svc.adapters.llm.rule_based import RuleBasedClassifier
from joyce_svc.adapters.llm.anthropic_intent import AnthropicClassifier
from joyce_svc.adapters.llm.ensemble import EnsembleClassifier

# Rate limiter
from joyce_svc.ports.rate_limit import RateLimiterPort
from joyce_svc.adapters.rate_limit.inproc import InProcRateLimiter


@lru_cache(maxsize=1)
def get_tools() -> ToolsPort:
    """
    File-backed tools only: load JSON from DATA_ROOT. Deterministic fallbacks are handled
    inside the file-backed adapter (graceful defaults) without a separate mock adapter.
    """
    data_root = Path(settings.DATA_ROOT)
    return FileBackedToolsAdapter(str(data_root))


@lru_cache(maxsize=1)
def get_metrics() -> MetricsPort:
    # No-op metrics by default; pluggable later
    return NoopMetrics()


@lru_cache(maxsize=1)
def get_memory() -> MemoryPort:
    # In-proc memory is instantiated but feature-gated via settings.MEMORY_ENABLED
    # Callers should check flags before using it to avoid side-effects.
    return InprocMemoryStore(window=settings.MEMORY_WINDOW, ttl_min=settings.MEMORY_TTL_MIN)


@lru_cache(maxsize=1)
def get_intent_classifier() -> IntentClassifier:
    # Always create a rule-based classifier
    rule = RuleBasedClassifier()
    # Optionally add Anthrop ic fallback when enabled and API key likely configured
    if settings.JOYCE_LLM_CLASSIFIER:
        llm: Optional[AnthropicClassifier]
        try:
            llm = AnthropicClassifier(
                model=settings.INTENT_LLM_MODEL,
                timeout_s=max(0.1, settings.INTENT_LLM_TIMEOUT_MS / 1000.0),
            )
        except Exception:
            llm = None
        if llm is not None:
            return EnsembleClassifier(primary=rule, fallback=llm)
    return rule


@lru_cache(maxsize=1)
def get_rate_limiter() -> RateLimiterPort:
    # Per-minute, per-key in-process limiter (window = 60s)
    return InProcRateLimiter(limit_per_min=settings.RATE_LIMIT_PER_MIN)
