import pytest
from httpx import AsyncClient, ASGITransport


@pytest.mark.asyncio
async def test_health_ok(test_app):
    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.get("/health")
        assert resp.status_code == 200
        data = resp.json()
        assert data["status"] == "ok"
        assert isinstance(data["version"], str)
        assert isinstance(data["uptimeSec"], int)
