# Joyce Agent Guide

## Project overview
- `joyce-svc` is a FastAPI microservice that powers <PERSON>, the advisor agent that streams `advisor_v1` frames over Server-Sent Events and sits behind a Node proxy.
- Deterministic orchestration is implemented with LangGraph state machines (`src/joyce_svc/agent`), leaning on typed Pydantic models to enforce the streaming contract and to keep flows rule-first.
- Ports/adapters (`src/joyce_svc/ports` and `src/joyce_svc/adapters`) isolate side effects: file-backed data access, intent classification options (rule-based or Anthropic fallback), in-memory session memory, metrics, and rate limiting.
- Middleware layers provide production guardrails: correlation IDs, structured timing logs, JWT auth, and an in-process rate limiter before requests reach the chat or health routers.
- Data for responses is loaded from `DATA_ROOT` (defaults to `/data`; `dev-data/` contains local samples) so the agent can assemble evidence-backed actions, sources, and narratives.

## Build and test commands
- Set up the environment: `python3 -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt`.
- Run the service locally with hot-reload path support: `uvicorn --app-dir src joyce_svc.main:app --host 0.0.0.0 --port 8080 --proxy-headers --log-level info`.
- Docker image for deploy parity: `docker build -t joyce-svc .` then `docker run -p 8080:8080 --env-file .env joyce-svc` (mount `/data` for real company data).
- Execute the full automated test suite: `pytest -q`.

## Code style guidelines
- Keep modules type-hinted and prefer Pydantic models for request/response schemas; state transitions live in typed LangGraph nodes rather than ad-hoc dicts.
- Follow the existing ports/adapters dependency injection pattern in `src/joyce_svc/di.py` so new integrations remain swappable and testable.
- Middleware ordering matters: correlation → timing → auth → rate limit → routes; preserve this sequencing when adding new middleware.
- Use structured logging via `joyce_svc.logging.get_logger` and include `correlationId`/context fields to match current observability expectations.
- Tests rely on deterministic behavior; avoid introducing side effects that bypass the rule-first flows without gating them behind feature flags or ports.

## Coding practices
- Meaningful naming conventions: choose descriptive names for modules, classes, functions, and variables; avoid unexplained abbreviations or single-letter identifiers.
- Keep functions/methods small and focused: apply the single-responsibility principle so each callable has one clear purpose and remains easy to test.
- DRY (Don't Repeat Yourself): encapsulate repeated logic in shared helpers or adapters instead of copying blocks of code.
- Consistent formatting and indentation: match the repository’s existing Python formatting (Black-compatible spacing, 4-space indents) for readability.
- Write self-documenting code: prioritize expressive structure and naming over heavy inline comments.
- Handle errors gracefully: surface actionable messages, wrap external calls defensively, and integrate with existing error taxonomy/middleware when possible.
- Write tests: extend unit, integration, or API suites as appropriate to cover new behavior and prevent regressions.
- Refactor regularly: improve structure without changing observable behavior to keep flows maintainable as features evolve.
- Prioritize readability: favor clarity and maintainability over clever constructs; optimize only when profiling data supports it.

## Testing instructions
- Install dev dependencies (see above), ensure `.env` mirrors `.env.local`, and point `DATA_ROOT` at `dev-data/` for deterministic fixtures during local runs.
- Run targeted suites as needed: `pytest -q tests/unit`, `pytest -q tests/integration`, or `pytest -q tests/api`.
- SSE contract regressions are caught by `tests/api/test_chat_sse.py`; use it when changing streaming behavior or middleware that touches chat responses.
- Integration flows (investor snapshot, margin driver, execution proposal) live under `tests/integration/`; update/add coverage when modifying LangGraph nodes or DI wiring.
- For debugging, add `-vv` or `--maxfail=1` to `pytest` and use `pytest-asyncio` fixtures already provided in the suite.

## Security considerations
- JWT verification is enforced by `AuthMiddleware`; production deployments must set `OIDC_ISSUER`, `OIDC_AUDIENCE`, and either `OIDC_JWKS_URL` or `OIDC_JWKS_PATH`. Leave `AUTH_REQUIRED=true` outside of local dev.
- Rate limiting (`RateLimitMiddleware`) guards against brute force and is identity-aware; treat it as per-tenant/user and ensure any proxy forwards client IP headers safely.
- CORS is restricted through `ALLOWED_ORIGINS`/`ALLOWED_ORIGIN_REGEX`; review before exposing new environments.
- Secrets such as `ANTHROPIC_API_KEY` or JWKS files should be supplied via environment variables or mounted volumes—never committed.
- SSE responses surface auth failures as structured error frames; confirm upstream proxies stream responses without buffering and strip sensitive exception messages before they reach clients.
