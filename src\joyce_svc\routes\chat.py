from __future__ import annotations

import asyncio
import json
import time
from typing import As<PERSON><PERSON><PERSON><PERSON>, Dict, List, Optional

from fastapi import APIRouter, HTTPException, Request, status
from sse_starlette.sse import EventSourceResponse

from joyce_svc.config import settings
from joyce_svc.logging import get_logger
from joyce_svc.models.schemas import Action, AdvisorFrame, ChatRequest, Meta, Timing, SourceRef
from joyce_svc.agent.graph import get_graph_app
from joyce_svc.errors import ToolError

router = APIRouter(tags=["chat"])
logger = get_logger(__name__)


def _frame_json(frame: AdvisorFrame) -> str:
    # Ensure compact JSON for SSE payload
    return frame.model_dump_json(exclude_none=True)


@router.post("/chat")
async def chat(request: Request, body: ChatRequest):
    """
    Streams advisor_v1 frames via Server-Sent Events (SSE).

    Behavior (MVP):
    - Emits: start → chunk → final frames
    - Echoes user's message in a consultant-style acknowledgment
    - Includes correlationId on every frame
    - On auth errors, emits {type:"error"} as the first/only frame and closes
    """

    # SSE generator
    async def event_stream() -> AsyncGenerator[Dict[str, str], None]:
        t0 = time.time()
        cid: Optional[str] = getattr(request.state, "correlation_id", None)

        # If auth error captured by middleware, emit error frame and close
        auth_error: Optional[Exception] = getattr(request.state, "auth_error", None)
        if auth_error is not None:
            logger.warning("Auth error on streaming endpoint; emitting error frame")
            error_frame = AdvisorFrame(
                type="error",
                content="Unauthorized",
                meta=Meta(correlationId=cid),
            )
            yield {"event": "message", "data": _frame_json(error_frame)}
            return

        # Validate consistency between JWT claims (if present) and body.
        # The request body remains source of truth; we only warn on mismatches for MVP.
        tenant_from_jwt = getattr(request.state, "tenant_from_jwt", None)
        user_id_from_jwt = getattr(request.state, "user_id", None)
        user_role_from_jwt = getattr(request.state, "user_role", None)

        try:
            if tenant_from_jwt and tenant_from_jwt != body.tenantId:
                logger.warning(
                    "tenantId mismatch between JWT and body (body is authoritative)",
                    extra={"path": "/v1/chat", "tenant_jwt": tenant_from_jwt, "tenant_body": body.tenantId},
                )
            if user_id_from_jwt and user_id_from_jwt != body.user.id:
                logger.warning(
                    "user.id mismatch between JWT and body (body is authoritative)",
                    extra={"path": "/v1/chat", "user_id_jwt": user_id_from_jwt, "user_id_body": body.user.id},
                )
            if user_role_from_jwt and user_role_from_jwt != body.user.role:
                logger.warning(
                    "user.role mismatch between JWT and body (body is authoritative)",
                    extra={"path": "/v1/chat", "user_role_jwt": user_role_from_jwt, "user_role_body": body.user.role},
                )
        except Exception:
            # Defensive: do not let logging inconsistencies break the stream
            pass

        # Emit start frame quickly to satisfy TTFB; optionally a tiny artificial delay for realism
        start_frame = AdvisorFrame(type="start", content="", meta=Meta(correlationId=cid))
        yield {"event": "message", "data": _frame_json(start_frame)}

        if settings.START_CHUNK_DELAY_MS > 0:
            await asyncio.sleep(settings.START_CHUNK_DELAY_MS / 1000.0)

        # First chunk - acknowledgment
        ttfb_ms = int((time.time() - t0) * 1000)
        chunk1 = AdvisorFrame(
            type="chunk",
            content="Acknowledged. Drafting your consultant-style brief...",
            meta=Meta(correlationId=cid, timing=Timing(ttfbMs=ttfb_ms)),
        )
        yield {"event": "message", "data": _frame_json(chunk1)}

        # Optional small pause for streaming feel, still well under perf guardrails
        await asyncio.sleep(0.1)

        # Second chunk - echo user's message with slight consultant tone
        echoed = body.message.strip()
        chunk2 = AdvisorFrame(
            type="chunk",
            content=f"Noted your request: “{echoed}”. Compiling succinct, evidence-aware highlights...",
            meta=Meta(correlationId=cid),
        )
        yield {"event": "message", "data": _frame_json(chunk2)}

        # Invoke LangGraph deterministically to produce final response, actions, and sources
        initial_state = {
            "query": body.message,
            "intent": None,
            "context": {
                "tenantId": body.tenantId,
                "threadId": body.threadId,
                "uiContext": body.uiContext.model_dump(),
                "user": {"id": body.user.id, "role": body.user.role},
            },
            "tools_to_call": [],
            "evidence": [],
            "response": "",
            "actions": [],
        }

        app_graph = get_graph_app()
        # Run graph with timeout guard; optionally emit simple tool frames when streaming-from-graph is enabled
        try:
            graph_timeout = settings.graph_timeout_seconds()
            if settings.STREAM_FROM_GRAPH:
                # Tool start frame (minimal)
                tool_start = AdvisorFrame(type="tool", content="Running agent graph...", meta=Meta(correlationId=cid))
                yield {"event": "message", "data": _frame_json(tool_start)}
                final_state = await asyncio.wait_for(
                    asyncio.to_thread(app_graph.invoke, initial_state),
                    timeout=graph_timeout,
                )
                tool_end = AdvisorFrame(type="tool", content="Agent graph completed.", meta=Meta(correlationId=cid))
                yield {"event": "message", "data": _frame_json(tool_end)}
            else:
                final_state = await asyncio.wait_for(
                    asyncio.to_thread(app_graph.invoke, initial_state),
                    timeout=graph_timeout,
                )
        except asyncio.TimeoutError:
            logger.warning("Graph execution timed out")
            error_frame = AdvisorFrame(
                type="error",
                content="Timeout",
                meta=Meta(correlationId=cid),
            )
            yield {"event": "message", "data": _frame_json(error_frame)}
            return
        except ToolError as e:
            logger.warning("Graph tool error", extra={"path": "/v1/chat"})
            error_frame = AdvisorFrame(
                type="error",
                content="Tool error",
                meta=Meta(correlationId=cid),
            )
            yield {"event": "message", "data": _frame_json(error_frame)}
            return
        except Exception as e:
            logger.exception("Graph execution failed")
            error_frame = AdvisorFrame(
                type="error",
                content="Internal error",
                meta=Meta(correlationId=cid),
            )
            yield {"event": "message", "data": _frame_json(error_frame)}
            return

        # Normalize to dict
        if hasattr(final_state, "model_dump"):
            data = final_state.model_dump()
        elif isinstance(final_state, dict):
            data = final_state
        else:
            data = dict(final_state)

        final_text = data.get("response") or "Drafted response."
        raw_actions = data.get("actions") or []
        actions: List[Action] = []
        if settings.ENABLE_UI_ACTIONS:
            for a in raw_actions:
                if isinstance(a, Action):
                    actions.append(a)
                else:
                    try:
                        actions.append(Action(**a))
                    except Exception:
                        continue

        raw_sources = data.get("evidence") or []
        sources: List[SourceRef] = []
        for s in raw_sources:
            if isinstance(s, SourceRef):
                sources.append(s)
            else:
                try:
                    sources.append(SourceRef(**s))
                except Exception:
                    continue

        final_frame = AdvisorFrame(
            type="final",
            content=final_text,
            actions=actions,
            sources=sources,
            meta=Meta(correlationId=cid),
        )
        yield {"event": "message", "data": _frame_json(final_frame)}

    # Create SSE response with appropriate headers
    response = EventSourceResponse(
        event_stream(),
        media_type="text/event-stream; charset=utf-8",
        ping=15000,  # keep-alive pings to help some proxies
    )

    # Structured log markers
    logger.info("SSE stream opened", extra={"path": "/v1/chat"})
    # When client disconnects, EventSourceResponse will cancel the generator; capture at task level
    async def _on_disconnect():
        while True:
            if await request.is_disconnected():
                logger.info("SSE stream disconnected", extra={"path": "/v1/chat"})
                break
            await asyncio.sleep(1.0)

    # Fire-and-forget watcher; do not block the response
    asyncio.create_task(_on_disconnect())

    return response
