from __future__ import annotations

import time
from collections import defaultdict, deque
from typing import Deque, Dict

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse
import jwt

from joyce_svc.config import settings
from joyce_svc.logging import get_logger
from joyce_svc.di import get_rate_limiter, get_metrics

logger = get_logger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Very simple in-process per-IP rate limiter.
    - Window: 60 seconds
    - Limit: settings.RATE_LIMIT_PER_MIN requests per IP per minute
    NOTE: This is per-process only and resets on restart (acceptable for MVP).
    """

    WINDOW_SEC = 60

    def __init__(self, app) -> None:
        super().__init__(app)

    def _get_client_ip(self, request: Request) -> str:
        # Respect X-Forwarded-For if present (via proxy); else use client host
        xff = request.headers.get("x-forwarded-for") or request.headers.get("X-Forwarded-For")
        if xff:
            # Take first IP in list
            return xff.split(",")[0].strip()
        client = request.client
        return client.host if client else "unknown"

    async def dispatch(self, request: Request, call_next):
        # Build identity key:
        # 1) Prefer tenantId:userId from auth middleware (verified)
        # 2) Else attempt best-effort extraction from Bearer JWT (unverified) to support ordering edge cases
        # 3) Fallback to client IP
        tenant = getattr(request.state, "tenant_from_jwt", None)
        user = getattr(request.state, "user_id", None)

        if not (tenant and user):
            # Best-effort parse of Authorization header without verification (for rate-limit identity only)
            try:
                auth = request.headers.get("authorization") or request.headers.get("Authorization")
                if auth and auth.lower().startswith("bearer "):
                    token = auth.split(" ", 1)[1].strip()
                    claims = jwt.decode(token, options={"verify_signature": False, "verify_aud": False})
                    tenant = claims.get("tenantId") or claims.get("tenant") or claims.get("tid") or tenant
                    user = claims.get("sub") or claims.get("user_id") or claims.get("uid") or user
            except Exception:
                pass

        if tenant and user:
            key = f"{tenant}:{user}"
        else:
            key = self._get_client_ip(request)

        limiter = get_rate_limiter()
        if not limiter.allow(key=key):
            try:
                metrics = get_metrics()
                metrics.inc_rate_limit_hits()
            except Exception:
                pass
            logger.warning(
                "Rate limit exceeded",
                extra={"client_ip": self._get_client_ip(request), "path": str(request.url.path), "status_code": 429},
            )
            return JSONResponse(
                status_code=429,
                content={"detail": "Too Many Requests"},
            )

        return await call_next(request)
