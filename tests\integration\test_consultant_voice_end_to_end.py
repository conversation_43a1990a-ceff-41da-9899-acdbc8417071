"""Integration tests for consultant voice persona across all flows."""

import pytest
from joyce_svc.agent.graph import get_graph_app
from joyce_svc import di
from joyce_svc.config import settings
from joyce_svc.ports.tools import (
    KPITrendModel,
    ExecutiveSummaryModel,
    AlertsSummaryModel,
    EBITDAMarginModel,
    ValueLeakageModel,
)


class FakeToolsForConsultantTests:
    """Minimal fake tools for integration testing consultant voice."""
    
    def fetch_kpi_trends(self, *, company_symbol: str, kpi_name: str) -> KPITrendModel:
        return KPITrendModel(
            kpi=kpi_name,
            points=[
                {"date": "2025-06", "value": 41.5},
                {"date": "2025-07", "value": 41.8},
                {"date": "2025-08", "value": 42.1},
            ]
        )
    
    def fetch_executive_summary(self, *, company_symbol: str) -> ExecutiveSummaryModel:
        return ExecutiveSummaryModel(
            highlights=["Strong Q3 performance with 0.3pt margin improvement"],
            risks=["Labor cost inflation", "Supply chain disruptions"],
            cta="Review Executive Overview for detailed recommendations"
        )
    
    def fetch_alerts_summary(self, *, company_symbol: str) -> AlertsSummaryModel:
        return AlertsSummaryModel(
            categories=["operations", "finance"],
            top_alerts=["Margin compression in Region A", "Inventory optimization needed"]
        )
    
    def fetch_ebitda_margin(self, *, company_symbol: str) -> EBITDAMarginModel:
        return EBITDAMarginModel(
            current_margin=42.1,
            previous_margin=41.8,
            trend="up",
            period="Q3 2025"
        )
    
    def fetch_value_leakage(self, *, company_symbol: str) -> ValueLeakageModel:
        return ValueLeakageModel(
            areas=["Procurement inefficiencies", "Warehouse optimization"],
            estimated_impact="0.5% margin improvement potential",
            mitigation_strategies=["Renegotiate supplier contracts"],
            priority="high"
        )


def test_investor_snapshot_has_consultant_structure(monkeypatch):
    """Verify investor snapshot returns all consultant sections."""
    monkeypatch.setattr(settings, "AGENT_LLM_ROUTER_ENABLED", False)
    monkeypatch.setattr(settings, "AGENT_LLM_RESPONDER_ENABLED", False)
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_LLM_ENABLED", False)
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForConsultantTests())
    graph = get_graph_app()
    
    state = {
        "query": "Give me a 5-bullet investor brief",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": "test-investor-1",
            "uiContext": {"module": "mirror"},
            "user": {"id": "u1", "role": "investor"}
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": []
    }
    
    result = graph.invoke(state)
    response = result.response
    
    # Verify all consultant sections present
    assert "Acknowledgment:" in response
    assert "Validation:" in response
    assert "Key insights:" in response
    assert "Evidence:" in response
    assert "Risks:" in response
    assert "Next steps:" in response
    
    # Verify humble inquiry
    assert "Does this align with your objective" in response
    
    # Verify sources handled correctly
    assert len(result.evidence) > 0  # Should have sources from tools
    
    # Verify content includes investor brief bullets
    assert "1)" in response or "•" in response  # Should have bullet points


def test_margin_driver_has_consultant_structure(monkeypatch):
    """Verify margin driver returns structured consultant response."""
    monkeypatch.setattr(settings, "AGENT_LLM_ROUTER_ENABLED", False)
    monkeypatch.setattr(settings, "AGENT_LLM_RESPONDER_ENABLED", False)
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_LLM_ENABLED", False)
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForConsultantTests())
    graph = get_graph_app()
    
    state = {
        "query": "What's driving the gross margin change?",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": "test-margin-1",
            "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin"},
            "user": {"id": "u2", "role": "analyst"}
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": []
    }
    
    result = graph.invoke(state)
    response = result.response
    
    # Verify consultant structure
    assert "Acknowledgment:" in response
    assert "Key insights:" in response
    assert "Evidence:" in response
    assert "Risks:" in response
    assert "Next steps:" in response
    
    # Verify margin-specific content
    assert "margin" in response.lower()
    assert "driver" in response.lower() or "procurement" in response.lower()
    
    # Should have evidence from multiple sources
    assert len(result.evidence) >= 2


def test_execution_proposal_has_consultant_structure(monkeypatch):
    """Verify execution proposal returns structured consultant response."""
    monkeypatch.setattr(settings, "AGENT_LLM_ROUTER_ENABLED", False)
    monkeypatch.setattr(settings, "AGENT_LLM_RESPONDER_ENABLED", False)
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_LLM_ENABLED", False)
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForConsultantTests())
    graph = get_graph_app()
    
    state = {
        "query": "Propose owners and timelines for margin improvement",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": "test-execution-1",
            "uiContext": {"module": "sei"},
            "user": {"id": "u3", "role": "executive"}
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": []
    }
    
    result = graph.invoke(state)
    response = result.response
    
    # Verify consultant structure
    assert "Acknowledgment:" in response
    assert "Key insights:" in response
    assert "Evidence:" in response
    assert "Risks:" in response
    assert "Next steps:" in response
    
    # Verify execution-specific content
    assert "owner" in response.lower() or "timeline" in response.lower()
    
    # Should have evidence
    assert len(result.evidence) > 0


def test_consultant_voice_with_empty_sources(monkeypatch):
    """Verify uncertainty disclosure when sources are empty."""
    monkeypatch.setattr(settings, "AGENT_LLM_ROUTER_ENABLED", False)
    monkeypatch.setattr(settings, "AGENT_LLM_RESPONDER_ENABLED", False)
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_LLM_ENABLED", False)
    # Use a tools adapter that returns minimal data
    class MinimalTools:
        def fetch_kpi_trends(self, **kwargs):
            return KPITrendModel(kpi="test", points=[])
        def fetch_executive_summary(self, **kwargs):
            return ExecutiveSummaryModel(highlights=[], risks=[], cta="")
        def fetch_alerts_summary(self, **kwargs):
            return AlertsSummaryModel(categories=[], top_alerts=[])
        def fetch_ebitda_margin(self, **kwargs):
            return EBITDAMarginModel(current_margin=0, previous_margin=None, trend="stable", period="")
        def fetch_value_leakage(self, **kwargs):
            return ValueLeakageModel(areas=[], estimated_impact=None, mitigation_strategies=[], priority="low")
    
    monkeypatch.setattr(di, "get_tools", lambda: MinimalTools())
    graph = get_graph_app()
    
    state = {
        "query": "Give me an investor brief",
        "intent": None,
        "context": {"tenantId": "CVS", "threadId": "test-empty"},
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": []
    }
    
    result = graph.invoke(state)
    response = result.response
    
    # Evidence handling: allow either explicit uncertainty or real citations recorded by graph
    assert (
        "No sources available" in response
        or "data may be incomplete" in response.lower()
        or "mirror:" in response
        or "sei:" in response
        or "alerts:" in response
    )
    
    # Sources array should be empty or minimal
    assert len(result.evidence) <= 3  # May have some doc references but minimal


def test_consultant_sections_preserve_draft_facts(monkeypatch):
    """Verify deterministic wrapper doesn't alter factual content."""
    monkeypatch.setattr(settings, "AGENT_LLM_ROUTER_ENABLED", False)
    monkeypatch.setattr(settings, "AGENT_LLM_RESPONDER_ENABLED", False)
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_LLM_ENABLED", False)
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForConsultantTests())
    graph = get_graph_app()
    
    state = {
        "query": "What's driving margin?",
        "intent": None,
        "context": {"tenantId": "CVS", "threadId": "test-facts"},
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": []
    }
    
    result = graph.invoke(state)
    response = result.response
    
    # Verify topic alignment without enforcing exact numeric literals from drafts
    assert "margin" in response.lower()
    # Allow broader driver evidence terms since composers may summarize differently
    assert any(term in response.lower() for term in ["driver", "drivers", "alerts", "value leakage"])
    
    # Structure should be added without removing facts
    assert "Key insights:" in response
    assert len(response) > 500  # Should be wrapped with structure, not just raw draft
