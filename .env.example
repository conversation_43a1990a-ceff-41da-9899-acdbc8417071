# Local Development Defaults (safe, offline by default)
# Copy to .env for local runs:  cp .env.example .env

# Core
LOG_LEVEL=info
PORT=8080
AUTH_REQUIRED=false

# CORS (adjust as needed)
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000

# Data root (uses repo dev data by default)
DATA_ROOT=${PWD}/dev-data

# Behavior & perf
ENABLE_UI_ACTIONS=true
STREAM_FROM_GRAPH=false
START_CHUNK_DELAY_MS=200
REQUEST_TIMEOUT_S=6
RATE_LIMIT_PER_MIN=60

# Auth (OIDC/JWT) - leave unset for local unauth requests (AUTH_REQUIRED=false)
# OIDC_ISSUER=
# OIDC_AUDIENCE=
# OIDC_JWKS_URL=
# OIDC_JWKS_PATH=

# Memory (MVP in-proc)
MEMORY_ENABLED=false
MEMORY_WINDOW=8
MEMORY_TTL_MIN=30

# LLM intent classifier (OFF by default for local)
JOYCE_LLM_CLASSIFIER=false
INTENT_LLM_MODEL=claude-sonnet-4-20250514

# Consultant persona (JOYCE-009)
CONSULTANT_VOICE_ENABLED=true
CONSULTANT_VOICE_LLM_ENABLED=false
# If unset, CONSULTANT_VOICE_MODEL defaults to INTENT_LLM_MODEL
# CONSULTANT_VOICE_MODEL=claude-sonnet-4-20250514
CONSULTANT_VOICE_TIMEOUT_MS=800

# Secrets (leave unset locally unless testing with real providers)
# ANTHROPIC_API_KEY=
# OPENAI_API_KEY=

# Metrics placeholder
METRICS_ENABLED=false
