"""SSE API tests for investor snapshot flow."""

import json
import pytest
from starlette.testclient import TestClient
from joyce_svc.main import app
from joyce_svc.config import settings


def test_investor_snapshot_sse_final_frame_completeness(monkeypatch):
    """Test that investor snapshot SSE final frame has all required fields per spec."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "investor"},
        "threadId": "thread-investor-1",
        "uiContext": {"module": "mirror"},
        "message": "Give me a 5-bullet investor brief",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # Verify frame sequence
        assert len(frames) >= 3
        assert frames[0]["type"] == "start"
        assert frames[-1]["type"] == "final"
        
        # Verify final frame completeness
        final = frames[-1]
        
        # Content must have 5 numbered bullets
        assert "content" in final
        content = final["content"]
        lines = content.splitlines()
        numbered_lines = [line for line in lines if line.strip() and line.strip()[0].isdigit()]
        assert len(numbered_lines) == 5, f"Expected 5 numbered bullets in final content"
        
        # Must include EBITDA proxy disclosure
        assert "EBITDA margin" in content
        assert "proxy" in content.lower() or "proxied" in content.lower()
        
        # Actions must include "Open Executive Overview"
        assert "actions" in final
        assert isinstance(final["actions"], list)
        assert len(final["actions"]) >= 1
        assert final["actions"][0]["label"] == "Open Executive Overview"
        assert final["actions"][0]["href"] == "/sei-report/executive-summary"
        
        # Sources must be non-empty with expected docIds
        assert "sources" in final
        assert isinstance(final["sources"], list)
        assert len(final["sources"]) > 0
        
        # Check for expected docIds (using actual registry values)
        doc_ids = {source["docId"] for source in final["sources"]}
        assert "mirror:kpiTrends" in doc_ids
        assert "sei:executive-summary" in doc_ids
        assert "alerts:summary" in doc_ids
        
        # Meta must have correlationId
        assert "meta" in final
        assert "correlationId" in final["meta"]


def test_investor_snapshot_sse_frame_ordering(monkeypatch):
    """Test that SSE frames arrive in correct order for investor snapshot."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "investor"},
        "threadId": "thread-investor-2",
        "uiContext": {"module": "mirror"},
        "message": "investor snapshot",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # Verify ordering: start → chunk(s) → final
        assert frames[0]["type"] == "start"
        
        # Middle frames should be chunks or tools
        for frame in frames[1:-1]:
            assert frame["type"] in ["chunk", "tool"]
        
        # Last frame must be final
        assert frames[-1]["type"] == "final"
        
        # All frames should have consistent correlationId
        correlation_ids = {frame.get("meta", {}).get("correlationId") for frame in frames}
        correlation_ids.discard(None)
        assert len(correlation_ids) == 1, "All frames should have the same correlationId"
