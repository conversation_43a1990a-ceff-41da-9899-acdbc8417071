from __future__ import annotations

from typing import Any, Dict, List

from joyce_svc.adapters.links.basic import DeepLinkBuilder
from joyce_svc.models.flows import InvestorBriefModel
from joyce_svc.models.schemas import Action, SourceRef


def compose_investor_snapshot(ctx: Dict[str, Any], sources: List[SourceRef]) -> InvestorBriefModel:
    """
    Compose a 5-bullet investor snapshot using deterministic data already fetched into ctx.
    - Bullet 1: Revenue snapshot from KPI trends (last vs previous point)
    - Bullet 2: Margin snapshot using EBITDA margin as a proxy for gross margin (with disclosure)
    - Bullet 3: Top alerts (1–2)
    - Bullet 4: Risks (1–2)
    - Bullet 5: CTA
    Never hallucinate numbers; gracefully degrade when data is missing.
    """
    safe_ctx = ctx or {}

    # Revenue snapshot from trends
    points = ((safe_ctx.get("mirror") or {}).get("trends") or {}).get("points") or []
    values: List[float] = []
    for p in points:
        if isinstance(p, dict):
            v = p.get("value")
            if isinstance(v, (int, float)):
                values.append(float(v))
    if len(values) >= 2:
        prev, curr = values[-2], values[-1]
        delta = round(curr - prev, 2)
        sign = "↑" if delta > 0 else ("↓" if delta < 0 else "→")
        revenue_bullet = f"Revenue: {sign} {abs(delta)} vs prior period (based on KPI trend)."
    elif len(values) == 1:
        revenue_bullet = "Revenue: single data point available; insufficient history for change vs prior period."
    else:
        revenue_bullet = "Revenue: trend data not available."

    # Margin snapshot (EBITDA as proxy for gross margin)
    margin = ((safe_ctx.get("mirror") or {}).get("ebitda_margin") or {}) or {}
    cm = margin.get("current_margin")
    pm = margin.get("previous_margin")
    trend = margin.get("trend", "stable")
    period = margin.get("period", "current period")
    if isinstance(cm, (int, float)) and isinstance(pm, (int, float)):
        delta = round(cm - pm, 2)
        sign = "↑" if delta > 0 else ("↓" if delta < 0 else "→")
        margin_bullet = (
            f"Margin: Gross margin (proxied by EBITDA) {trend} ({sign} {abs(delta)} pts) in {period}. "
            f"Disclosure: Gross margin is proxied by EBITDA margin for MVP."
        )
    elif isinstance(cm, (int, float)):
        margin_bullet = (
            f"Margin: Gross margin (proxied by EBITDA) is {cm} in {period} (trend: {trend}). "
            f"Disclosure: Gross margin is proxied by EBITDA margin for MVP."
        )
    else:
        margin_bullet = (
            "Margin: data unavailable. Disclosure: Gross margin is proxied by EBITDA margin for MVP."
        )

    # Alerts (top 1–2)
    alerts = ((safe_ctx.get("alerts") or {}).get("top_alerts") or [])
    top_alerts: List[str] = []
    if isinstance(alerts, list):
        top_alerts = [str(a) for a in alerts[:2] if isinstance(a, (str, int, float))]
    alerts_bullet = (
        f"Alerts: {('; '.join(top_alerts))}."
        if top_alerts
        else "Alerts: no active alerts surfaced."
    )

    # Risks (1–2 from SEI)
    risks = ((safe_ctx.get("sei") or {}).get("risks") or [])
    top_risks: List[str] = []
    if isinstance(risks, list):
        top_risks = [str(r) for r in risks[:2] if isinstance(r, (str, int, float))]
    risks_bullet = (
        f"Risks: {('; '.join(top_risks))}."
        if top_risks
        else "Risks: no material risks highlighted."
    )

    # CTA
    cta = ((safe_ctx.get("sei") or {}).get("cta")) or "Review Executive Overview for deeper context and recommendations."
    cta_bullet = f"CTA: {cta}"

    bullets = [revenue_bullet, margin_bullet, alerts_bullet, risks_bullet, cta_bullet]
    content = "\n".join(f"{i}) {text}" for i, text in enumerate(bullets, start=1))

    # UI Action
    ui = (safe_ctx.get("uiContext") or {})
    links = DeepLinkBuilder(ui_context=ui)
    actions = [Action(label="Open Executive Overview", href=links.executive_overview())]

    return InvestorBriefModel(content=content, actions=actions)
