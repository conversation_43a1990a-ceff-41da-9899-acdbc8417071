import json
import uuid
import base64
import time

import pytest
from httpx import AsyncClient, ASGITransport

from joyce_svc.config import settings


def _parse_sse_text(text: str):
    """Extract JSON payloads from SSE response text."""
    frames = []
    for line in text.splitlines():
        line = line.strip()
        if line.startswith("data:"):
            payload = line[len("data:") :].strip()
            frames.append(json.loads(payload))
    return frames


def _sample_body():
    return {
        "tenantId": "t_abc",
        "user": {"id": "u_123", "role": "Investor"},
        "threadId": "th_789",
        "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin", "route": "/digital-mirror?kpi=ebitda-margin"},
        "message": "Give me a 5-bullet investor brief for this month.",
        "caps": {"allowBenchmarks": False},
        "response_format": "advisor_v1",
    }


@pytest.mark.asyncio
async def test_chat_sse_unauthorized_stream(test_app):
    """
    With AUTH_REQUIRED=True and no bearer token, middleware defers 401 for SSE,
    and route emits an 'error' frame and closes.
    """
    settings.AUTH_REQUIRED = True
    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.post("/v1/chat", json=_sample_body())
        assert resp.status_code == 200
        assert resp.headers["content-type"].startswith("text/event-stream")
        # Correlation header should be present
        assert "X-Correlation-Id" in resp.headers

        frames = _parse_sse_text(resp.text)
        assert len(frames) == 1
        assert frames[0]["type"] == "error"
        assert frames[0]["content"] == "Unauthorized"
        assert "meta" in frames[0] and "correlationId" in frames[0]["meta"]


@pytest.mark.asyncio
async def test_chat_sse_missing_message_returns_422(test_app):
    """
    FastAPI with Pydantic should return 422 if required fields are missing.
    """
    settings.AUTH_REQUIRED = False
    body = _sample_body()
    del body["message"]  # Remove required field

    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.post("/v1/chat", json=body)
        assert resp.status_code == 422


@pytest.mark.asyncio
async def test_chat_sse_happy_path_no_auth(test_app):
    """
    With AUTH_REQUIRED=False, stream should emit start -> chunk -> final with correlationId in meta.
    """
    settings.AUTH_REQUIRED = False
    fixed_cid = str(uuid.uuid4())
    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.post(
            "/v1/chat",
            json=_sample_body(),
            headers={"X-Correlation-Id": fixed_cid},
        )
        assert resp.status_code == 200
        assert resp.headers["content-type"].startswith("text/event-stream")
        # Correlation header echoed back
        assert resp.headers.get("X-Correlation-Id") == fixed_cid

        frames = _parse_sse_text(resp.text)
        # Expect at least 3 frames: start, one or more chunk(s), final
        assert len(frames) >= 3
        assert frames[0]["type"] == "start"
        # All frames should include meta.correlationId
        for f in frames:
            assert "meta" in f and f["meta"].get("correlationId") == fixed_cid

        # Final frame checks
        final = frames[-1]
        assert final["type"] == "final"
        assert "content" in final and isinstance(final["content"], str) and len(final["content"]) > 0
        assert "sources" in final and isinstance(final["sources"], list)
        assert "actions" in final and isinstance(final["actions"], list)


# ---- Optional: valid JWT happy path using in-memory RSA key and local JWKS file ----

try:
    from cryptography.hazmat.primitives.asymmetric import rsa
    from cryptography.hazmat.primitives import serialization
    HAVE_CRYPTO = True
except Exception:  # pragma: no cover - cryptography not available
    HAVE_CRYPTO = False


def _b64url_uint(n: int) -> str:
    b = n.to_bytes((n.bit_length() + 7) // 8, "big")
    return base64.urlsafe_b64encode(b).decode("ascii").rstrip("=")


@pytest.mark.asyncio
@pytest.mark.skipif(not HAVE_CRYPTO, reason="cryptography not available")
async def test_chat_sse_with_valid_jwt(test_app, tmp_path):
    import jwt

    # Generate RSA key
    key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
    private_key_pem = key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption(),
    )
    public_numbers = key.public_key().public_numbers()
    kid = "test-kid"

    # Write JWKS with the public key
    jwks = {
        "keys": [
            {
                "kty": "RSA",
                "kid": kid,
                "use": "sig",
                "alg": "RS256",
                "n": _b64url_uint(public_numbers.n),
                "e": _b64url_uint(public_numbers.e),
            }
        ]
    }
    jwks_path = tmp_path / "jwks.json"
    jwks_path.write_text(json.dumps(jwks), encoding="utf-8")

    # Configure settings to use local JWKS and strict issuer/audience
    iss = "https://issuer.test"
    aud = "joyce-audience"
    settings.OIDC_JWKS_PATH = str(jwks_path)
    settings.OIDC_JWKS_URL = None
    settings.OIDC_ISSUER = iss
    settings.OIDC_AUDIENCE = aud
    settings.AUTH_REQUIRED = True

    # Create a valid JWT
    now = int(time.time())
    claims = {
        "sub": "u_123",
        "role": "Investor",
        "tenantId": "t_abc",
        "iss": iss,
        "aud": aud,
        "iat": now,
        "exp": now + 300,
    }
    token = jwt.encode(claims, private_key_pem, algorithm="RS256", headers={"kid": kid})

    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.post(
            "/v1/chat",
            json=_sample_body(),
            headers={"Authorization": f"Bearer {token}"},
        )
        assert resp.status_code == 200
        frames = _parse_sse_text(resp.text)
        assert len(frames) >= 3
        assert frames[0]["type"] == "start"
        assert frames[-1]["type"] == "final"
