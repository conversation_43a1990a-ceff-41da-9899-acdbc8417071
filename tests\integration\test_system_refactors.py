import json
import time
import uuid

import pytest
from httpx import AsyncClient, ASGITransport

from joyce_svc.config import settings


def _parse_sse_text(text: str):
    frames = []
    for line in text.splitlines():
        line = line.strip()
        if line.startswith("data:"):
            payload = line[len("data:") :].strip()
            frames.append(json.loads(payload))
    return frames


def _sample_body():
    return {
        "tenantId": "t_abc",
        "user": {"id": "u_123", "role": "Investor"},
        "threadId": "th_789",
        "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin", "route": "/digital-mirror?kpi=ebitda-margin"},
        "message": "Give me a 5-bullet investor brief for this month.",
        "caps": {"allowBenchmarks": False},
        "response_format": "advisor_v1",
    }


@pytest.mark.asyncio
async def test_stream_from_graph_flag_off_preserves_frames(test_app):
    # Default is False; ensure behavior unchanged (no 'tool' frames)
    settings.AUTH_REQUIRED = False
    settings.STREAM_FROM_GRAPH = False
    fixed_cid = str(uuid.uuid4())

    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.post(
            "/v1/chat",
            json=_sample_body(),
            headers={"X-Correlation-Id": fixed_cid},
        )
        assert resp.status_code == 200
        frames = _parse_sse_text(resp.text)
        kinds = [f["type"] for f in frames]
        assert "tool" not in kinds
        assert kinds[0] == "start"
        assert "final" in kinds


@pytest.mark.asyncio
async def test_stream_from_graph_flag_on_emits_tool_frames(test_app):
    # Enable flag and ensure we see 'tool' frames between chunks and final
    settings.AUTH_REQUIRED = False
    settings.STREAM_FROM_GRAPH = True
    fixed_cid = str(uuid.uuid4())

    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.post(
            "/v1/chat",
            json=_sample_body(),
            headers={"X-Correlation-Id": fixed_cid},
        )
        assert resp.status_code == 200
        frames = _parse_sse_text(resp.text)
        kinds = [f["type"] for f in frames]
        assert "tool" in kinds, "Expected tool frames when STREAM_FROM_GRAPH is true"
        # Ensure final is present and comes after at least one tool frame
        assert kinds[-1] == "final"
        assert kinds.index("tool") < kinds.index("final")


def _b64url_uint(n: int) -> str:
    import base64
    b = n.to_bytes((n.bit_length() + 7) // 8, "big")
    return base64.urlsafe_b64encode(b).decode("ascii").rstrip("=")


@pytest.mark.asyncio
async def test_rate_limiter_keys_by_tenant_user_when_present(monkeypatch, test_app, tmp_path):
    # Monkeypatch the limiter in middleware to capture the key used
    class FakeLimiter:
        def __init__(self):
            self.last_key = None

        def allow(self, *, key: str) -> bool:
            self.last_key = key
            return True

    fake = FakeLimiter()
    import joyce_svc.middleware.rate_limit as rl
    monkeypatch.setattr(rl, "get_rate_limiter", lambda: fake)

    # Generate a valid JWT similar to existing test
    try:
        from cryptography.hazmat.primitives.asymmetric import rsa
        from cryptography.hazmat.primitives import serialization
    except Exception:
        pytest.skip("cryptography not available")

    import jwt

    key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
    private_key_pem = key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption(),
    )
    public_numbers = key.public_key().public_numbers()
    kid = "test-kid-rl"

    jwks = {
        "keys": [
            {
                "kty": "RSA",
                "kid": kid,
                "use": "sig",
                "alg": "RS256",
                "n": _b64url_uint(public_numbers.n),
                "e": _b64url_uint(public_numbers.e),
            }
        ]
    }
    jwks_path = tmp_path / "jwks.json"
    jwks_path.write_text(json.dumps(jwks), encoding="utf-8")

    iss = "https://issuer.test"
    aud = "joyce-audience"
    settings.OIDC_JWKS_PATH = str(jwks_path)
    settings.OIDC_JWKS_URL = None
    settings.OIDC_ISSUER = iss
    settings.OIDC_AUDIENCE = aud
    settings.AUTH_REQUIRED = True

    now = int(time.time())
    claims = {
        "sub": "u_123",
        "role": "Investor",
        "tenantId": "t_abc",
        "iss": iss,
        "aud": aud,
        "iat": now,
        "exp": now + 300,
    }
    token = jwt.encode(claims, private_key_pem, algorithm="RS256", headers={"kid": kid})

    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.post(
            "/v1/chat",
            json=_sample_body(),
            headers={"Authorization": f"Bearer {token}"},
        )
        assert resp.status_code == 200
        # Fake limiter recorded the composed key
        assert fake.last_key == "t_abc:u_123"


@pytest.mark.asyncio
async def test_http_timing_middleware_logs_elapsed_ms(test_app, caplog):
    settings.AUTH_REQUIRED = False
    transport = ASGITransport(app=test_app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        resp = await client.get("/health")
        assert resp.status_code == 200

    # Look for our structured timing log
    msgs = [r.message for r in caplog.records if getattr(r, "message", None)]
    assert any(m == "http_timing" for m in msgs), "Expected http_timing log entry"
