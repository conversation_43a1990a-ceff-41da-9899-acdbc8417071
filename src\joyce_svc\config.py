from __future__ import annotations

import os
from functools import lru_cache
from typing import List, Optional

from pydantic import AnyHttpUrl, Field, ValidationInfo, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Service configuration parsed from environment variables.
    """

    model_config = SettingsConfigDict(env_prefix="", case_sensitive=False, env_file=".env")

    # App
    PORT: int = Field(default=8080)
    LOG_LEVEL: str = Field(default="info", description="Python log level (debug, info, warning, error)")
    JOYCE_ENABLED: bool = Field(default=True, description="Feature flag for Node proxy integration")
    JOYCE_LLM_CLASSIFIER: bool = Field(
        default=False, description="Enable LLM fallback for intent classification (default: False)"
    )
    INTENT_LLM_MODEL: str = Field(
        default="claude-sonnet-4-********", description="Model for LLM intent classification when enabled"
    )
    INTENT_LLM_TIMEOUT_MS: int = Field(
        default=600, description="Timebox for LLM intent classification (ms)"
    )

    # Agent orchestration (LLM-first toggles)
    AGENT_LLM_ROUTER_ENABLED: bool = Field(
        default=True,
        description=(
            "If true, consult an LLM planner before rule-based intent routing."
            " Falls back to deterministic plan on any error."
        ),
    )
    AGENT_ROUTER_MODEL: Optional[str] = Field(
        default=None,
        description="Model used for the LLM router (defaults to INTENT_LLM_MODEL when unset)",
    )
    AGENT_ROUTER_TIMEOUT_MS: int = Field(
        default=3000,
        description="Timeout for the planner LLM call (ms)",
    )
    AGENT_LLM_RESPONDER_ENABLED: bool = Field(
        default=True,
        description=(
            "If true, attempt to let an LLM craft the final consultant response before"
            " applying deterministic fallbacks."
        ),
    )
    AGENT_RESPONDER_MODEL: Optional[str] = Field(
        default=None,
        description="Model for LLM response composer (defaults to CONSULTANT_VOICE_MODEL)",
    )
    AGENT_RESPONDER_TIMEOUT_MS: int = Field(
        default=15000,
        description="Timeout for the LLM response composer (ms)",
    )
    
    # API Keys (auto-loaded from env)
    ANTHROPIC_API_KEY: Optional[str] = Field(
        default=None, 
        description="Anthropic API key for Claude models (auto-loaded from env)"
    )

    # CORS
    ALLOWED_ORIGINS: str = Field(
        default="http://localhost:5173,http://localhost:3000,https://app-dev.rejoyce.ai,https://app-stg.rejoyce.ai,https://app.rejoyce.ai",
        description="Comma-separated list of allowed origins for CORS",
    )
    ALLOWED_ORIGIN_REGEX: Optional[str] = Field(
        default=None,
        description='Optional regex for allowed origins (e.g., ^https://(app|app-stg|app-dev)\\.rejoyce\\.ai$)',
    )

    # Auth (OIDC / JWKS)
    OIDC_ISSUER: Optional[str] = Field(default=None, description="OIDC issuer to validate in JWT")
    OIDC_AUDIENCE: Optional[str] = Field(default=None, description="OIDC audience to validate in JWT")
    OIDC_JWKS_URL: Optional[AnyHttpUrl] = Field(default=None, description="JWKS URL for verifying JWTs")
    OIDC_JWKS_PATH: Optional[str] = Field(
        default=None,
        description="Local filesystem path to JWKS JSON (for dev/tests); if set, takes precedence over URL",
    )
    AUTH_REQUIRED: bool = Field(
        default=True,
        description="If false, skip JWT verification (DEV ONLY). In prod, should remain True.",
    )

    # Rate limiting
    RATE_LIMIT_PER_MIN: int = Field(default=60, description="Simple per-IP limit per rolling minute")

    # Streaming/Perf
    START_CHUNK_DELAY_MS: int = Field(
        default=200, description="Artificial small delay before first chunk to simulate TTFB, if needed"
    )
    MAX_TTFB_MS: int = Field(default=1800, description="Guardrail for first token time")
    P95_GOAL_MS: int = Field(default=6000, description="End-to-end latency p95 goal for MVP")

    # Feature flags / behavior
    ENABLE_UI_ACTIONS: bool = Field(
        default=True, description="Gate emission of UI actions in final frames"
    )
    STREAM_FROM_GRAPH: bool = Field(
        default=False, description="If true, use graph-native streaming path for /v1/chat"
    )

    # Consultant persona (voice)
    CONSULTANT_VOICE_ENABLED: bool = Field(
        default=True, 
        description="Enable consultant persona wrapping of final content"
    )
    CONSULTANT_VOICE_LLM_ENABLED: bool = Field(
        default=False,
        description="If true, use LLM to refine persona wrapping (fallback to deterministic on timeout/error)",
    )
    CONSULTANT_VOICE_MODEL: Optional[str] = Field(
        default=None,
        description="Model for consultant voice refinement; if unset, defaults to INTENT_LLM_MODEL",
    )
    CONSULTANT_VOICE_TIMEOUT_MS: int = Field(
        default=800, description="Timeout for LLM persona refinement (ms)"
    )

    # Request/timeouts
    REQUEST_TIMEOUT_S: int = Field(
        default=6, description="Overall request timeout guard (seconds)"
    )

    # Memory (scaffold)
    MEMORY_ENABLED: bool = Field(default=False, description="Enable session memory integration")
    MEMORY_WINDOW: int = Field(default=8, description="Memory turn window per thread")
    MEMORY_TTL_MIN: int = Field(default=30, description="Memory TTL in minutes")

    # Metrics/observability (noop by default)
    METRICS_ENABLED: bool = Field(default=False, description="Enable metrics port usage")

    # Data
    DATA_ROOT: str = Field(default="/data", description="Root directory for company data files")

    @property
    def allowed_origins_list(self) -> List[str]:
        return [o.strip() for o in self.ALLOWED_ORIGINS.split(",") if o.strip()]

    @field_validator("LOG_LEVEL")
    @classmethod
    def _normalize_log_level(cls, v: str, info: ValidationInfo) -> str:
        return v.lower()

    @field_validator("AUTH_REQUIRED")
    @classmethod
    def _warn_if_auth_disabled(cls, v: bool) -> bool:
        if not v:
            # Printing here is acceptable since logging may not be configured yet
            print("WARNING: AUTH_REQUIRED is False. JWT verification is disabled (DEV ONLY).")
        return v

    def auth_configured(self) -> bool:
        """
        Returns True if we have enough config to verify JWTs (when required).
        """
        if not self.AUTH_REQUIRED:
            return False
        return bool((self.OIDC_JWKS_PATH or self.OIDC_JWKS_URL) and self.OIDC_AUDIENCE and self.OIDC_ISSUER)

    @model_validator(mode="after")
    def _derive_consultant_voice_model(self) -> "Settings":
        """
        If CONSULTANT_VOICE_MODEL is unset or blank, default it to INTENT_LLM_MODEL.
        This aligns persona default with the intent LLM model specified in env/.env.local.
        """
        cvm = (self.CONSULTANT_VOICE_MODEL or "").strip()
        if not cvm:
            self.CONSULTANT_VOICE_MODEL = self.INTENT_LLM_MODEL
        if not (self.AGENT_ROUTER_MODEL or "").strip():
            self.AGENT_ROUTER_MODEL = self.INTENT_LLM_MODEL
        if not (self.AGENT_RESPONDER_MODEL or "").strip():
            self.AGENT_RESPONDER_MODEL = self.CONSULTANT_VOICE_MODEL
        return self

    def graph_timeout_seconds(self) -> float:
        """
        Calculate an application-level timeout budget for LangGraph execution.

        When LLM orchestration is enabled we automatically budget additional time to
        allow for remote model latency and SDK retries.
        """
        base = float(self.REQUEST_TIMEOUT_S)
        if self.AGENT_LLM_ROUTER_ENABLED:
            base = max(base, 3.0 + (self.AGENT_ROUTER_TIMEOUT_MS / 1000.0))
        if self.AGENT_LLM_RESPONDER_ENABLED or self.CONSULTANT_VOICE_LLM_ENABLED:
            responder_budget = max(self.AGENT_RESPONDER_TIMEOUT_MS, self.CONSULTANT_VOICE_TIMEOUT_MS)
            base = max(base, 8.0 + (responder_budget / 1000.0))
        return base


@lru_cache(maxsize=1)
def get_settings() -> Settings:
    # Allow loading from a .env file if present
    # pydantic-settings automatically loads .env if model_config.env_file is set,
    # but we'll support python-dotenv style manual load if needed.
    # Keep simple for now.
    return Settings()


# Global accessor for convenience
settings = get_settings()
