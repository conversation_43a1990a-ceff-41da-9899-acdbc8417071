from __future__ import annotations

import json
import logging
import sys
from datetime import datetime, timezone
from typing import Any, Dict, Optional

import contextvars

# Correlation ID context for structured logs
_correlation_id_ctx: contextvars.ContextVar[Optional[str]] = contextvars.ContextVar(
    "correlation_id", default=None
)


def set_correlation_id(correlation_id: Optional[str]) -> None:
    _correlation_id_ctx.set(correlation_id)


def get_correlation_id() -> Optional[str]:
    return _correlation_id_ctx.get()


class JSONFormatter(logging.Formatter):
    """
    Minimal JSON formatter for structured logging.
    Ensures correlationId is included on every line (if present in contextvar).
    """

    def __init__(self, *, include_timestamp: bool = True) -> None:
        super().__init__()
        self.include_timestamp = include_timestamp

    def format(self, record: logging.LogRecord) -> str:
        log: Dict[str, Any] = {
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
        }

        if self.include_timestamp:
            # ISO8601 with Z for UTC
            log["ts"] = datetime.now(timezone.utc).isoformat()

        # Attach correlation id
        cid = get_correlation_id()
        if cid:
            log["correlationId"] = cid

        # Common HTTP fields if present in record.extra
        for field in ("method", "path", "status_code", "client_ip", "elapsed_ms"):
            val = getattr(record, field, None)
            if val is not None:
                log[field] = val

        # Exception info
        if record.exc_info:
            log["exc_info"] = self.formatException(record.exc_info)

        return json.dumps(log, ensure_ascii=False)


def setup_logging(level: str = "info") -> None:
    """
    Configure root logger and uvicorn loggers to use JSON formatting.
    """
    lvl = getattr(logging, level.upper(), logging.INFO)

    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(JSONFormatter())

    root = logging.getLogger()
    # Clear existing handlers to avoid duplicates in reloader contexts
    for h in list(root.handlers):
        root.removeHandler(h)
    root.setLevel(lvl)
    root.addHandler(handler)

    # Align uvicorn loggers with our formatter
    for name in ("uvicorn", "uvicorn.error", "uvicorn.access"):
        logger = logging.getLogger(name)
        for h in list(logger.handlers):
            logger.removeHandler(h)
        logger.setLevel(lvl)
        logger.addHandler(handler)
        logger.propagate = False


def get_logger(name: str) -> logging.Logger:
    return logging.getLogger(name)
