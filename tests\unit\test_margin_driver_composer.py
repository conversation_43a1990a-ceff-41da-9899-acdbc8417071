import re

from joyce_svc.agent.flows.margin_driver.composer import compose_margin_driver


def test_compose_margin_driver_builds_summary_drivers_actions():
    ctx = {
        "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin"},
        "mirror": {
            "ebitda_margin": {
                "current_margin": 42.1,
                "previous_margin": 41.8,
                "trend": "up",
                "period": "Q3 2025",
            },
            "value_leakage": {
                "areas": ["Procurement inefficiencies", "Shrink"],
                "mitigation_strategies": ["Renegotiate contracts", "Tighten vendor funding controls"],
                "priority": "high",
            },
        },
        "alerts": {
            "top_alerts": ["Margin compression in Region A", "Inventory buildup"],
        },
        "analysis": {
            # ensure composer prefers analysis.summary when present
            "summary": "EBITDA margin up (↑ 0.3 pts) in Q3 2025. Drivers from alerts and value leakage highlighted.",
            # and uses precomputed drivers if provided
            "drivers": ["Margin compression in Region A", "Leakage: Procurement inefficiencies"],
        },
    }

    out = compose_margin_driver(ctx, [])

    # Content should be a concise paragraph including summary, top drivers, and next actions.
    assert isinstance(out.content, str) and len(out.content) > 20
    assert "EBITDA margin" in out.content
    assert "Top drivers:" in out.content
    assert "Next actions:" in out.content

    # Ensure leakage drivers are surfaced distinctly
    assert "Leakage:" in out.content

    # Deep link action should point to Digital Mirror with KPI derived from uiContext.entityId
    assert len(out.actions) >= 1
    first = out.actions[0]
    assert first.label == "Open Digital Mirror (EBITDA margin)"
    assert first.href == "/digital-mirror?kpi=ebitda-margin"


def test_compose_margin_driver_handles_missing_data_gracefully():
    # Minimal context: no alerts, no leakage; composer should still produce a sensible summary and action.
    ctx = {
        "uiContext": {"module": "mirror"},  # no entityId; expect default KPI in deep-link
        "mirror": {
            "ebitda_margin": {
                "current_margin": 40.0,
                "previous_margin": 40.0,
                "trend": "stable",
                "period": "Current",
            },
        },
        # alerts and value_leakage missing
    }

    out = compose_margin_driver(ctx, [])

    assert isinstance(out.content, str) and len(out.content) > 10
    assert "EBITDA margin" in out.content
    assert "Top drivers:" in out.content  # still renders the header
    assert "Next actions:" in out.content  # deterministic fallback actions when none provided

    # Deep link defaults to ebitda-margin when entityId is missing
    assert len(out.actions) >= 1
    first = out.actions[0]
    assert first.label == "Open Digital Mirror (EBITDA margin)"
    assert first.href == "/digital-mirror?kpi=ebitda-margin"
