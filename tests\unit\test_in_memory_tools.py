import pytest
from joyce_svc.adapters.tools.in_memory import InMemoryToolsAdapter
from joyce_svc.ports.tools import KPITrendModel


def test_fetch_kpi_trends_loads_data():
    adapter = InMemoryToolsAdapter()
    # Assuming 'cvs' is a valid company symbol with corresponding data
    kpi_trend = adapter.fetch_kpi_trends(company_symbol="cvs", kpi_name="ebitda-margin")
    assert isinstance(kpi_trend, KPITrendModel)
    assert kpi_trend.kpi == "ebitda-margin"
    assert len(kpi_trend.points) > 0
    assert kpi_trend.points[0]["value"] > 0
