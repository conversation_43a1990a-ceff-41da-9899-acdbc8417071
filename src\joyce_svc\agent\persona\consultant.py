from __future__ import annotations

from typing import Any, Dict, List, Optional
import re

from joyce_svc.config import settings
from joyce_svc.agent.types import Intent
from joyce_svc.models.schemas import Action, SourceRef


CONSULTANT_SYSTEM_PROMPT = """
You are <PERSON>, a senior McKinsey consultant. Structure responses using:
1. Brief acknowledgment and question validation
2. Key insights (pyramid principle - conclusion first)
3. Supporting evidence with citations (use docId labels)
4. Risks and considerations
5. Clear next steps with owners

Rules:
- Use humble inquiry ("ask before tell") to validate direction.
- Enforce MECE bullets (no overlapping categories).
- Never fabricate numbers; when data is incomplete, explicitly state uncertainty.
- Keep it concise and executive-ready.
""".strip()


def _normalize_citations(sources: List[SourceRef]) -> List[str]:
    items: List[str] = []
    for s in sources or []:
        try:
            label = s.docId
            title = s.title or ""
            if title:
                items.append(f"{label} — {title}")
            else:
                items.append(label)
        except Exception:
            continue
    # De-duplicate while preserving order
    seen = set()
    deduped: List[str] = []
    for it in items:
        if it in seen:
            continue
        seen.add(it)
        deduped.append(it)
    return deduped


def _first_sentence(text: str) -> str:
    t = (text or "").strip()
    if not t:
        return ""
    # Take up to the first newline or sentence terminator
    for sep in [".\n", ". ", "\n"]:
        if sep in t:
            head = t.split(sep, 1)[0]
            return (head + ".").strip()
    # Fallback: cap length a bit
    return (t[:240] + ("..." if len(t) > 240 else "")).strip()


def _extract_next_actions_from_draft(draft: str) -> Optional[str]:
    # If draft already contains a "Next actions:" line, reuse it under NEXT STEPS to keep facts intact.
    for line in (draft or "").splitlines():
        l = line.strip()
        if l.lower().startswith("next actions:"):
            return l
    return None


def _derive_risks(ctx: Dict[str, Any]) -> List[str]:
    # Prefer risks from SEI context when available (1–2 items)
    risks = ((ctx or {}).get("sei") or {}).get("risks") or []
    out: List[str] = []
    if isinstance(risks, list):
        for r in risks:
            if isinstance(r, (str, int, float)):
                out.append(str(r))
            if len(out) >= 2:
                break
    if out:
        return out
    # Conservative generic risk when none present
    return ["Data coverage may be incomplete; validate figures in source dashboards"]


def _strip_number_prefix(text: str) -> str:
    i = 0
    s = text or ""
    n = len(s)
    # Skip leading whitespace
    while i < n and s[i].isspace():
        i += 1
    # Read consecutive digits
    j = i
    while j < n and s[j].isdigit():
        j += 1
    # If we saw digits and next char is ')', strip them and following whitespace
    if j > i and j < n and s[j] == ')':
        j += 1
        while j < n and s[j].isspace():
            j += 1
        return s[j:]
    return s

def _deterministic_consultant_wrap(
    *,
    draft: str,
    intent: Optional[Intent],
    ctx: Dict[str, Any],
    sources: List[SourceRef],
    actions: Optional[List[Action]] = None,
) -> str:
    # Acknowledgment & validation
    ui = (ctx or {}).get("uiContext") or {}
    module = ui.get("module") or "workspace"
    ack = f"Acknowledgment: Understood. Responding in consultant style for the {module} context."
    validation = "Validation: Does this align with your objective for this request?"

    # Key insights — keep the draft intact to preserve facts and exact phrases used by tests
    # Also surface a concise conclusion first line derived from the draft.
    conclusion = _first_sentence(draft)
    if conclusion:
        # Strip leading numbering like "1) " to avoid adding an extra numbered bullet
        conclusion_clean = _strip_number_prefix(conclusion)
        insights = f"Conclusion: {conclusion_clean}\n\n{draft}".strip()
    else:
        insights = draft.strip() or "No draft available."

    # Evidence — list real citations or an explicit disclaimer
    citations = _normalize_citations(sources)
    if citations:
        evidence = "Evidence: " + "; ".join(citations) + "."
    else:
        evidence = "Evidence: No sources available — data may be incomplete."

    # Risks
    risks_items = _derive_risks(ctx)
    risks_text = "Risks: " + "; ".join(risks_items) + "."

    # Next steps — reuse any explicit 'Next actions:' line from the draft; otherwise provide a minimal placeholder
    reuse = _extract_next_actions_from_draft(draft)
    if reuse:
        next_steps = f"Next steps: {reuse[len('Next actions:'):].strip()}"
        if not next_steps.strip().endswith("."):
            next_steps = next_steps.strip() + "."
    elif actions:
        labels = "; ".join(a.label for a in actions if getattr(a, "label", None))
        labels = labels or "Review linked follow-ups"
        next_steps = f"Next steps: {labels}."
    else:
        next_steps = "Next steps: Review deep links in actions and confirm ownership/timelines with functional leads."

    # Assemble sections
    parts = [
        ack,
        validation,
        "Key insights:",
        insights,
        evidence,
        risks_text,
        next_steps,
    ]
    return "\n\n".join(p for p in parts if p).strip()


def render_consultant_voice(
    *,
    draft: str,
    intent: Optional[Intent],
    ctx: Dict[str, Any],
    sources: List[SourceRef],
    actions: Optional[List[Action]] = None,
) -> str:
    """
    Render final content in the consultant persona. Keeps latency predictable:
    - If CONSULTANT_VOICE_ENABLED is False: return draft unchanged.
    - If CONSULTANT_VOICE_LLM_ENABLED is True: attempt LLM refinement (timeout-bounded), else fall back.
    - Otherwise: deterministic formatter wraps the draft into the 5 sections.
    """
    if not settings.CONSULTANT_VOICE_ENABLED:
        return draft

    if getattr(settings, "AGENT_LLM_RESPONDER_ENABLED", False):
        try:
            from joyce_svc.agent.llm_response import generate_llm_consultant_message

            llm_text = generate_llm_consultant_message(
                draft=draft,
                intent=intent,
                ctx=ctx,
                sources=sources,
                actions=actions or [],
            )
            if llm_text:
                return llm_text
        except Exception:
            # If anything unexpected happens, fall back to deterministic behavior.
            pass

    # Deterministic default path (LLM off by default for perf)
    if not getattr(settings, "CONSULTANT_VOICE_LLM_ENABLED", False):
        return _deterministic_consultant_wrap(draft=draft, intent=intent, ctx=ctx, sources=sources, actions=actions)

    # Optional LLM path (lazy import; never executed in tests unless explicitly enabled)
    try:
        system = CONSULTANT_SYSTEM_PROMPT
        citations = _normalize_citations(sources)
        citations_text = "; ".join(citations) if citations else "No sources available — data may be incomplete."
        ui = (ctx or {}).get("uiContext") or {}
        user_instructions = (
            f"Intent: {intent or 'unknown'}\n"
            f"UI context: module={ui.get('module')}, entityId={ui.get('entityId')}\n"
            f"Citations: {citations_text}\n"
            "Do not invent numbers. Keep the factual content from the DRAFT. "
            "Wrap into the 5-part consultant structure and include a brief validation question.\n"
            f"DRAFT:\n{draft}"
        )
        
        model_name = settings.CONSULTANT_VOICE_MODEL or "gpt-4o-mini"
        timeout_s = settings.CONSULTANT_VOICE_TIMEOUT_MS / 1000.0
        
        # Use Anthropic for Claude models
        if model_name and "claude" in model_name.lower():
            import os
            from anthropic import Anthropic  # type: ignore
            
            api_key = settings.ANTHROPIC_API_KEY or os.getenv("ANTHROPIC_API_KEY")
            if not api_key:
                raise ValueError("ANTHROPIC_API_KEY not found for Claude model")
            
            client = Anthropic(api_key=api_key)
            response = client.messages.create(
                model=model_name,
                max_tokens=1000,
                temperature=0.0,
                timeout=timeout_s,
                system=system,
                messages=[{
                    "role": "user",
                    "content": user_instructions
                }]
            )
            content = response.content[0].text if response.content else None
        else:
            # OpenAI path for non-Claude models
            from langchain_openai import ChatOpenAI  # type: ignore
            from langchain.prompts import ChatPromptTemplate  # type: ignore
            
            prompt = ChatPromptTemplate.from_messages(
                [("system", system), ("user", user_instructions)]
            )
            model = ChatOpenAI(
                model=model_name,
                temperature=0.0,
                timeout=timeout_s,
            )
            chain = prompt | model
            result = chain.invoke({})
            content = getattr(result, "content", None)
        
        if isinstance(content, str) and content.strip():
            return content.strip()
    except Exception:
        # Fall back silently to deterministic formatter on any error/timeout/import issue
        pass

    return _deterministic_consultant_wrap(draft=draft, intent=intent, ctx=ctx, sources=sources, actions=actions)
