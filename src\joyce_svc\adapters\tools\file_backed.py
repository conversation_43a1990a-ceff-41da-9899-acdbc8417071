from __future__ import annotations

import json
from pathlib import Path
from typing import Dict, Any, Optional
import structlog

from joyce_svc.config import settings
from joyce_svc.ports.tools import (
    ToolsPort,
    KPITrendModel,
    ExecutiveSummaryModel,
    AlertsSummaryModel,
    EBITDAMarginModel,
    ValueLeakageModel,
)

logger = structlog.get_logger(__name__)


def _parse_percent(value) -> float:
    """
    Parse a percent-like string (e.g., "6.8%") or numeric into float.
    Returns 0.0 on any parse issue.
    """
    try:
        if isinstance(value, (int, float)):
            return float(value)
        if isinstance(value, str):
            v = value.strip().replace("%", "")
            return float(v)
    except Exception:
        return 0.0
    return 0.0


_SEVERITY_ORDER = {"critical": 4, "high": 3, "medium": 2, "low": 1}


class FileBackedToolsAdapter(ToolsPort):
    """
    Tools adapter that reads company data from JSON files under settings.DATA_ROOT.

    Layout (per RP-78):
      /data/companies/{symbol}/
        era-report/executive-summary-dashboard.json
        executive-summary.json
        alerts.json

    Behavior:
      - If files/keys are missing, returns graceful defaults (so graph doesn't break)
      - Returns shapes aligned with ToolsPort Pydantic models
    """

    def __init__(self, data_root: Optional[str] = None) -> None:
        self.data_root = Path(data_root or settings.DATA_ROOT)

    # ---------- helpers ----------

    def _company_path(self, symbol: str) -> Path:
        return self.data_root / "companies" / symbol

    def _load_json(self, path: Path) -> Optional[Dict[str, Any]]:
        try:
            if not path.exists():
                logger.warning("Data file not found", file_path=str(path))
                return None
            with path.open("r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error("Failed to read/parse JSON", file_path=str(path), error=str(e))
            return None

    # ---------- ToolsPort implementation ----------

    def fetch_kpi_trends(self, *, company_symbol: str, kpi_name: str) -> KPITrendModel:
        """
        Return time series for a KPI. Supports multiple schemas:
        - RP-78 legacy: data["kpis"][kpi_name] with current/previous values
        - Rejoyce client data: data["chartConfigurations"]["kpiTrends"]["data"]["labels"/"datasets"]
          where dataset labels include "EBITDA Margin %" or "Revenue Growth %"
        """
        symbol = (company_symbol or "").lower()
        base = self._company_path(symbol)
        path = base / "era-report" / "executive-summary-dashboard.json"

        data = self._load_json(path)
        if not data:
            return KPITrendModel(
                kpi=kpi_name,
                points=[{"date": "2025-08", "value": 0.0, "note": "No data available"}],
            )

        # Preferred: chartConfigurations.kpiTrends
        try:
            cfg = (data.get("chartConfigurations") or {}).get("kpiTrends") or {}
            labels = ((cfg.get("data") or {}).get("labels") or []) or []
            datasets = ((cfg.get("data") or {}).get("datasets") or []) or []
            # Choose dataset based on requested kpi_name
            target = None
            lname = (kpi_name or "").lower()
            for ds in datasets:
                label = str(ds.get("label") or "").lower()
                if "ebitda" in lname and "ebitda" in label:
                    target = ds
                    break
                if "revenue" in lname and "revenue" in label:
                    target = ds
                    break
            # Fallback: if not matched, prefer "EBITDA" dataset
            if target is None and datasets:
                for ds in datasets:
                    if "ebitda" in str(ds.get("label") or "").lower():
                        target = ds
                        break
                if target is None:
                    target = datasets[0]
            if target is not None and isinstance(labels, list):
                values = list(target.get("data") or [])
                points = []
                for i, lbl in enumerate(labels):
                    v = values[i] if i < len(values) else None
                    points.append({"date": str(lbl), "value": _parse_percent(v)})
                return KPITrendModel(kpi=kpi_name, points=points)
        except Exception:
            # fall through to legacy shape
            pass

        # Legacy: {"kpis": {"ebitda-margin": {...}}}
        kpis = data.get("kpis", {})
        kpi_data = kpis.get(kpi_name, {})
        if not kpi_data:
            logger.warning("KPI not found in dashboard", company_symbol=symbol, kpi_name=kpi_name)
            return KPITrendModel(
                kpi=kpi_name,
                points=[{"date": "2025-08", "value": 0.0, "note": "KPI not available"}],
            )

        current_value = kpi_data.get("current_value", 0.0)
        previous_value = kpi_data.get("previous_value", current_value)
        points = [
            {"date": "2025-06", "value": float(previous_value) if isinstance(previous_value, (int, float)) else 0.0},
            {"date": "2025-07", "value": float(previous_value) if isinstance(previous_value, (int, float)) else 0.0},
            {"date": "2025-08", "value": float(current_value) if isinstance(current_value, (int, float)) else 0.0},
        ]
        return KPITrendModel(kpi=kpi_name, points=points)

    def fetch_executive_summary(self, *, company_symbol: str) -> ExecutiveSummaryModel:
        """
        Executive summary supports both legacy and Rejoyce schemas.
        - Legacy keys: key_highlights, risks
        - Rejoyce schema: keyStrengths[].text for highlights, riskFlags[].text for risks
        """
        symbol = (company_symbol or "").lower()
        base = self._company_path(symbol)
        path = base / "executive-summary.json"

        data = self._load_json(path)
        if not data:
            return ExecutiveSummaryModel(
                highlights=["No data available for this company"],
                risks=["Data not found"],
                cta="Please check company symbol and try again",
            )

        # Legacy
        highlights = data.get("key_highlights") or data.get("highlights")
        risks = data.get("risks")

        # Rejoyce schema
        if not highlights:
            ks = data.get("keyStrengths") or []
            if isinstance(ks, list):
                highlights = [str(it.get("text")) for it in ks if isinstance(it, dict) and it.get("text")]
        if not risks:
            rf = data.get("riskFlags") or []
            if isinstance(rf, list):
                risks = [str(it.get("text")) for it in rf if isinstance(it, dict) and it.get("text")]

        # Fallback if still empty
        if not highlights:
            # take first 1-2 sentences from summaryAnalysis.text if present
            txt = (((data.get("summaryAnalysis") or {}).get("text")) or "").strip()
            highlights = [txt] if txt else ["No highlights available"]
        if not risks:
            risks = ["No material risks highlighted."]

        cta = "Review Executive Overview for deeper context and recommendations"

        return ExecutiveSummaryModel(
            highlights=list(highlights),
            risks=list(risks),
            cta=cta,
        )

    def fetch_alerts_summary(self, *, company_symbol: str) -> AlertsSummaryModel:
        """
        Alerts summary supports:
        - Legacy grouped dict: {"alerts": {"high_priority":[...], "operations":[...]}}
        - Rejoyce list schema: {"alerts":[{...}, {...}], "totalUnreadCount": ...}
        """
        symbol = (company_symbol or "").lower()
        base = self._company_path(symbol)
        path = base / "alerts.json"

        data = self._load_json(path)
        if not data:
            return AlertsSummaryModel(
                categories=["data"],
                top_alerts=["No alert data available for this company"],
            )

        raw_alerts = data.get("alerts")
        # Rejoyce: list of alert objects
        if isinstance(raw_alerts, list):
            categories = []
            top_alerts = []
            # Collect categories and choose top alerts by severity
            for a in raw_alerts:
                if not isinstance(a, dict):
                    continue
                cat = a.get("category")
                if isinstance(cat, str):
                    categories.append(cat)
            # Top alerts sorted by severity
            alerts_sorted = sorted(
                (a for a in raw_alerts if isinstance(a, dict)),
                key=lambda x: _SEVERITY_ORDER.get(str(x.get("severity") or "").lower(), 0),
                reverse=True,
            )
            for a in alerts_sorted:
                title = a.get("title") or a.get("description")
                if title:
                    top_alerts.append(str(title))
                if len(top_alerts) >= 3:
                    break
            if not categories:
                categories = ["general"]
            if not top_alerts:
                top_alerts = ["No active alerts"]
            return AlertsSummaryModel(categories=list(dict.fromkeys(categories)), top_alerts=top_alerts)

        # Legacy dict: categories map to lists
        alerts = raw_alerts if isinstance(raw_alerts, dict) else {}
        categories = [k for k, v in alerts.items() if isinstance(v, list)]
        top_alerts = []
        hp = alerts.get("high_priority")
        if isinstance(hp, list) and hp:
            top_alerts = list(hp[:3])
        else:
            for cat in categories:
                if len(top_alerts) >= 3:
                    break
                items = alerts.get(cat) or []
                if isinstance(items, list) and items:
                    top_alerts.extend(items[:2])
        if not categories:
            categories = ["general"]
        if not top_alerts:
            top_alerts = ["No active alerts"]
        return AlertsSummaryModel(categories=categories, top_alerts=top_alerts)

    def fetch_ebitda_margin(self, *, company_symbol: str) -> EBITDAMarginModel:
        """
        Pull EBITDA margin current/previous/trend/period from:
        - chartConfigurations.kpiTrends "EBITDA Margin %" series, last two points
        - OR executiveKPIs list item where metric == "EBITDA Margin"
        - OR legacy financial_metrics.ebitda_margin
        """
        symbol = (company_symbol or "").lower()
        base = self._company_path(symbol)
        path = base / "era-report" / "executive-summary-dashboard.json"

        data = self._load_json(path)
        if not data:
            return EBITDAMarginModel(
                current_margin=0.0,
                previous_margin=None,
                trend="stable",
                period="Current",
            )

        # Try chartConfigurations.kpiTrends
        try:
            cfg = (data.get("chartConfigurations") or {}).get("kpiTrends") or {}
            labels = ((cfg.get("data") or {}).get("labels") or []) or []
            datasets = ((cfg.get("data") or {}).get("datasets") or []) or []
            target = None
            for ds in datasets:
                if "ebitda" in str(ds.get("label") or "").lower():
                    target = ds
                    break
            if target is not None:
                series = list(target.get("data") or [])
                if series:
                    current = _parse_percent(series[-1])
                    previous = _parse_percent(series[-2]) if len(series) >= 2 else None
                    period = str(labels[-1]) if labels else "Current"
                    trend = "stable"
                    if previous is not None:
                        if current > previous:
                            trend = "up"
                        elif current < previous:
                            trend = "down"
                    return EBITDAMarginModel(
                        current_margin=current,
                        previous_margin=previous,
                        trend=trend,
                        period=period,
                    )
        except Exception:
            pass

        # Try executiveKPIs list
        try:
            ekpis = data.get("executiveKPIs") or []
            if isinstance(ekpis, list):
                for item in ekpis:
                    if str(item.get("metric") or "").lower().startswith("ebitda"):
                        current = _parse_percent(item.get("current"))
                        # previous not present; approximate from trends if available
                        previous = None
                        trend = str(item.get("trend") or "stable")
                        period = "Current"
                        return EBITDAMarginModel(
                            current_margin=current,
                            previous_margin=previous,
                            trend=trend,
                            period=period,
                        )
        except Exception:
            pass

        # Legacy fallback
        fm = data.get("financial_metrics", {}) or {}
        e = fm.get("ebitda_margin", {}) or {}
        current = e.get("current_margin", 0.0)
        previous = e.get("previous_margin")
        period = e.get("period", "Current")
        trend = "stable"
        try:
            if previous is not None and isinstance(current, (int, float)) and isinstance(previous, (int, float)):
                if current > previous:
                    trend = "up"
                elif current < previous:
                    trend = "down"
        except Exception:
            pass
        return EBITDAMarginModel(
            current_margin=float(current) if isinstance(current, (int, float)) else 0.0,
            previous_margin=float(previous) if isinstance(previous, (int, float)) else None,
            trend=str(trend),
            period=str(period),
        )

    def fetch_value_leakage(self, *, company_symbol: str) -> ValueLeakageModel:
        """
        Value leakage supports:
        - Rejoyce value-leakage-financial-impact.json (preferred)
        - Rejoyce executive-summary-dashboard.json under valueLeakageOverview.breakdown
        - Legacy value_leakage shape
        """
        symbol = (company_symbol or "").lower()
        base = self._company_path(symbol)

        # Preferred file
        path_vl = base / "era-report" / "value-leakage-financial-impact.json"
        data_vl = self._load_json(path_vl)

        if data_vl:
            try:
                cats = data_vl.get("leakageCategories") or []
                areas = [str(c.get("category")) for c in cats if isinstance(c, dict) and c.get("category")]
                # Aggregate recoveryPath (mitigation strategies)
                mitigation: list[str] = []
                for c in cats:
                    if not isinstance(c, dict):
                        continue
                    rec = ((c.get("details") or {}).get("recoveryPath")) or []
                    if isinstance(rec, list):
                        for r in rec:
                            if isinstance(r, str):
                                mitigation.append(r)
                mitigation = list(dict.fromkeys(mitigation))
                # Estimated impact from overview.totalLeakage.percentage if available
                est = None
                tl = ((data_vl.get("valueLeakageOverview") or {}).get("totalLeakage") or {})
                if isinstance(tl, dict):
                    est = tl.get("percentage") or tl.get("amount")
                    if est is not None and not isinstance(est, str):
                        try:
                            est = str(est)
                        except Exception:
                            est = None
                # Priority: highest among categories (critical/high/medium/low)
                pr = "medium"
                for c in cats:
                    p = str((c.get("priority") or "medium")).lower() if isinstance(c, dict) else "medium"
                    if _SEVERITY_ORDER.get(p, 2) > _SEVERITY_ORDER.get(pr, 2):
                        pr = p
                return ValueLeakageModel(
                    areas=areas,
                    estimated_impact=est,
                    mitigation_strategies=mitigation,
                    priority=pr,
                )
            except Exception:
                pass

        # Secondary source: executive-summary-dashboard.json
        path_dash = base / "era-report" / "executive-summary-dashboard.json"
        data_dash = self._load_json(path_dash)
        if data_dash:
            try:
                vov = data_dash.get("valueLeakageOverview") or {}
                breakdown = vov.get("breakdown") or []
                areas = []
                for b in breakdown:
                    if isinstance(b, dict) and b.get("category"):
                        areas.append(str(b["category"]))
                est = None
                total = vov.get("totalLeakage") or {}
                if isinstance(total, dict):
                    est = total.get("percentage") or total.get("value")
                    if est is not None and not isinstance(est, str):
                        try:
                            est = str(est)
                        except Exception:
                            est = None
                # Only return if we actually extracted meaningful info; otherwise fall through to legacy fields
                if areas or est:
                    return ValueLeakageModel(
                        areas=areas,
                        estimated_impact=est,
                        mitigation_strategies=[],
                        priority="medium",
                    )
            except Exception:
                pass

        # Legacy fallback
        path_legacy = path_dash
        data = data_dash or self._load_json(path_legacy)
        if not data:
            return ValueLeakageModel(
                areas=[],
                estimated_impact=None,
                mitigation_strategies=[],
                priority="medium",
            )
        vl = data.get("value_leakage", {}) or {}
        areas = vl.get("areas") or []
        if not isinstance(areas, list):
            areas = [str(areas)]
        mitigation = vl.get("mitigation_strategies") or []
        if not isinstance(mitigation, list):
            mitigation = [str(mitigation)]
        estimated = vl.get("estimated_impact")
        if estimated is not None and not isinstance(estimated, str):
            try:
                estimated = str(estimated)
            except Exception:
                estimated = None
        priority = vl.get("priority", "medium")
        return ValueLeakageModel(
            areas=list(areas),
            estimated_impact=estimated,
            mitigation_strategies=list(mitigation),
            priority=str(priority),
        )
