"""API tests for CORS headers."""

import pytest
from starlette.testclient import TestClient
from joyce_svc.main import app
from joyce_svc.config import settings


def test_cors_preflight_options(monkeypatch):
    """OPTIONS preflight should return proper CORS headers."""
    # Set allowed origins
    monkeypatch.setattr(
        settings,
        "ALLOWED_ORIGINS",
        "http://localhost:3000,https://app.example.com"
    )
    
    client = TestClient(app)
    
    # Preflight request
    response = client.options(
        "/v1/chat",
        headers={
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "POST",
            "Access-Control-Request-Headers": "content-type,x-correlation-id",
        }
    )
    
    assert response.status_code == 200
    
    # Check CORS headers
    assert response.headers.get("access-control-allow-origin") == "http://localhost:3000"
    assert "POST" in response.headers.get("access-control-allow-methods", "")
    assert "content-type" in response.headers.get("access-control-allow-headers", "").lower()


def test_cors_allowed_origin_validation(monkeypatch):
    """Only allowed origins should get CORS headers."""
    monkeypatch.setattr(
        settings,
        "ALLOWED_ORIGINS",
        "http://localhost:3000,https://app.example.com"
    )
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    # Request from allowed origin
    response = client.get(
        "/health",
        headers={"Origin": "http://localhost:3000"}
    )
    
    assert response.status_code == 200
    assert response.headers.get("access-control-allow-origin") == "http://localhost:3000"
    
    # Request from disallowed origin
    response = client.get(
        "/health",
        headers={"Origin": "http://evil.com"}
    )
    
    assert response.status_code == 200
    # Should not have CORS headers for disallowed origin
    assert "access-control-allow-origin" not in response.headers


def test_cors_regex_pattern_matching(monkeypatch):
    """CORS should support regex pattern matching."""
    monkeypatch.setattr(
        settings,
        "ALLOWED_ORIGIN_REGEX",
        r"^https://(app|app-stg|app-dev)\.rejoyce\.ai$"
    )
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    # Matching origins
    for origin in [
        "https://app.rejoyce.ai",
        "https://app-stg.rejoyce.ai",
        "https://app-dev.rejoyce.ai",
    ]:
        response = client.get(
            "/health",
            headers={"Origin": origin}
        )
        assert response.status_code == 200
        assert response.headers.get("access-control-allow-origin") == origin
    
    # Non-matching origins
    for origin in [
        "http://app.rejoyce.ai",  # Wrong protocol
        "https://evil.rejoyce.ai",  # Wrong subdomain
        "https://app.rejoyce.com",  # Wrong domain
    ]:
        response = client.get(
            "/health",
            headers={"Origin": origin}
        )
        assert response.status_code == 200
        assert "access-control-allow-origin" not in response.headers


def test_cors_credentials_support(monkeypatch):
    """CORS should support credentials when configured."""
    monkeypatch.setattr(
        settings,
        "ALLOWED_ORIGINS",
        "http://localhost:3000"
    )
    
    client = TestClient(app)
    
    response = client.options(
        "/v1/chat",
        headers={
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "POST",
        }
    )
    
    assert response.status_code == 200
    # With allow_credentials=False in app config, this header should be absent
    assert response.headers.get("access-control-allow-credentials") is None


def test_cors_exposed_headers(monkeypatch):
    """CORS should expose necessary headers."""
    monkeypatch.setattr(
        settings,
        "ALLOWED_ORIGINS",
        "http://localhost:3000"
    )
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    response = client.get(
        "/health",
        headers={"Origin": "http://localhost:3000"}
    )
    
    assert response.status_code == 200
    
    # Check exposed headers
    exposed = response.headers.get("access-control-expose-headers", "")
    assert "x-correlation-id" in exposed.lower()


def test_cors_max_age(monkeypatch):
    """CORS preflight should include max-age."""
    monkeypatch.setattr(
        settings,
        "ALLOWED_ORIGINS",
        "http://localhost:3000"
    )
    
    client = TestClient(app)
    
    response = client.options(
        "/v1/chat",
        headers={
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "POST",
        }
    )
    
    assert response.status_code == 200
    
    # Should have max-age for caching preflight
    max_age = response.headers.get("access-control-max-age")
    assert max_age is not None
    assert int(max_age) > 0


def test_cors_wildcard_not_used(monkeypatch):
    """CORS should not use wildcard (*) for security."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    # Even without origin restrictions configured
    monkeypatch.setattr(settings, "ALLOWED_ORIGINS", "")
    monkeypatch.setattr(settings, "ALLOWED_ORIGIN_REGEX", None)
    
    response = client.get(
        "/health",
        headers={"Origin": "http://any.origin.com"}
    )
    
    assert response.status_code == 200
    
    # Should not use wildcard
    allow_origin = response.headers.get("access-control-allow-origin", "")
    assert allow_origin != "*"


def test_cors_post_request_with_origin(monkeypatch):
    """POST requests should include CORS headers for allowed origins."""
    monkeypatch.setattr(
        settings,
        "ALLOWED_ORIGINS",
        "http://localhost:3000"
    )
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
    }
    
    with client.stream(
        "POST",
        "/v1/chat",
        json=req,
        headers={
            "Origin": "http://localhost:3000",
            "Accept": "text/event-stream",
        }
    ) as resp:
        assert resp.status_code == 200
        assert resp.headers.get("access-control-allow-origin") == "http://localhost:3000"
