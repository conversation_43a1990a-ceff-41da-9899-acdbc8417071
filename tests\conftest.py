import os
import sys

# Ensure src/ is on sys.path for imports when running tests from repo root
REPO_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), ".."))
SRC_PATH = os.path.join(REPO_ROOT, "src")
if SR<PERSON>_PATH not in sys.path:
    sys.path.insert(0, SRC_PATH)

import pytest  # noqa: E402
from joyce_svc.main import app  # noqa: E402
from joyce_svc.config import settings  # noqa: E402
from joyce_svc import di  # noqa: E402


@pytest.fixture(scope="session")
def test_app():
    """
    Return FastAPI app instance.
    """
    return app


@pytest.fixture(autouse=True, scope="session")
def _force_offline_llm_behavior():
    """
    Force offline behavior in tests to avoid external LLM/network calls and timeouts.
    Also reduce artificial TTFB to speed up SSE tests.
    """
    # Disable all LLM paths
    settings.CONSULTANT_VOICE_LLM_ENABLED = False
    settings.AGENT_LLM_ROUTER_ENABLED = False
    settings.AGENT_LLM_RESPONDER_ENABLED = False
    settings.JOYCE_LLM_CLASSIFIER = False
    # Remove API key so Anthropic SDK won't attempt network calls
    os.environ["ANTHROPIC_API_KEY"] = ""
    # Keep a small positive delay so ttfbMs > 0 in tests
    settings.START_CHUNK_DELAY_MS = 10
    # Clear DI caches to reflect updated settings
    if hasattr(di.get_intent_classifier, "cache_clear"):
        di.get_intent_classifier.cache_clear()
    yield
    if hasattr(di.get_intent_classifier, "cache_clear"):
        di.get_intent_classifier.cache_clear()


@pytest.fixture(autouse=True)
def _reset_auth_requirement():
    """
    Ensure tests start with default AUTH_REQUIRED configuration.
    Individual tests can override via monkeypatch if needed.
    """
    # Reset to default (True) between tests
    settings.AUTH_REQUIRED = True
    yield
