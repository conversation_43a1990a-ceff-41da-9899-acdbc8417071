from __future__ import annotations

from typing import Protocol


class RateLimiterPort(Protocol):
    """
    Abstract rate limiter. Implementations may be in-proc or shared (e.g., Redis).
    Keying is per logical identity key (tenantId:userId) when available, else IP.
    Windowing and limits are implementation-specific but typically 60s windows.
    """

    def allow(self, *, key: str) -> bool:
        """
        Returns True if the request for this key is allowed under the current window, else False.
        """
        ...
