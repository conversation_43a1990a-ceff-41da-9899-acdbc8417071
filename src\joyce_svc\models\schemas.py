from __future__ import annotations

from typing import List, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field


# ========== advisor_v1 request schema ==========


class UserInfo(BaseModel):
    model_config = ConfigDict(extra="forbid")

    id: str = Field(..., description="Stable user identifier")
    role: str = Field(..., description="Role propagated from JWT")


class UIContext(BaseModel):
    model_config = ConfigDict(extra="forbid")

    module: str = Field(..., description="UI module initiating the request")
    entityId: Optional[str] = Field(
        default=None, description="Optional entity identifier (e.g., KPI id)"
    )
    route: Optional[str] = Field(
        default=None, description="Optional current route for deep-links"
    )


class Caps(BaseModel):
    model_config = ConfigDict(extra="forbid")

    allowBenchmarks: Optional[bool] = Field(default=None)
    allowWeb: Optional[bool] = Field(default=None)


class ChatRequest(BaseModel):
    """
    Request schema for advisor_v1.

    Read-only in MVP. Validates basic fields and response_format version pin.
    """

    model_config = ConfigDict(extra="forbid")

    tenantId: str
    user: UserInfo
    threadId: str
    uiContext: UIContext
    message: str
    caps: Optional[Caps] = None
    response_format: Literal["advisor_v1"]


# ========== advisor_v1 streaming frame schema ==========


class Action(BaseModel):
    model_config = ConfigDict(extra="forbid")

    label: str
    href: str


class SourceRef(BaseModel):
    model_config = ConfigDict(extra="forbid")

    kind: Literal["internal", "external"]
    docId: str
    location: Optional[str] = None
    title: Optional[str] = None
    url: Optional[str] = None


class TokenCounts(BaseModel):
    model_config = ConfigDict(extra="forbid")

    prompt: Optional[int] = None
    completion: Optional[int] = None


class Timing(BaseModel):
    model_config = ConfigDict(extra="forbid")

    ttfbMs: Optional[int] = None


class Meta(BaseModel):
    model_config = ConfigDict(extra="forbid")

    correlationId: Optional[str] = None
    tokens: Optional[TokenCounts] = None
    timing: Optional[Timing] = None


class AdvisorFrame(BaseModel):
    """
    advisor_v1 streaming frame. Contract fidelity is prioritized.
    - type: "start" | "chunk" | "tool" | "final" | "error"
    - content: required on chunk/final, optional on tool
    - final MUST include authoritative actions[] and sources[] (even if empty)
    """

    model_config = ConfigDict(extra="forbid")

    type: Literal["start", "chunk", "tool", "final", "error"]
    content: Optional[str] = Field(
        default=None, description="Required on chunk/final; optional on tool"
    )
    actions: Optional[List[Action]] = None
    sources: Optional[List[SourceRef]] = None
    meta: Optional[Meta] = None
