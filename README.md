# rejoyce-joyce-agent — joyce-svc (FastAPI + SSE, advisor_v1)

Home of Joyce, the Rejoyce AI Agent — an advisor-style AI that interprets company data, reasons over KPIs, and streams structured, evidence-backed answers.

This repository contains the Python microservice "joyce-svc", designed to sit behind the Node/Express proxy. It exposes a health endpoint and a streaming chat endpoint that emits advisor_v1 frames over Server-Sent Events (SSE). The MVP focuses on contract fidelity (advisor_v1), auth, correlation IDs, structured logging, CORS, rate limiting, and developer experience.

Deterministic orchestration is rule-first (LangGraph + typed state). Optional LLM usage includes: (a) intent classification fallback when rules cannot decide, and (b) consultant voice refinement of the final draft. Both are opt-in, timeboxed, and fall back to deterministic behavior on timeout/error.

## Endpoints

- GET /health — liveness + version + uptime
- POST /v1/chat — SSE stream emitting advisor_v1 frames:
  - Frames: start → chunk → final (and error on auth failure)
  - Optional tool frames when STREAM_FROM_GRAPH=true

## Advisor_v1 Contract (summary)

Request (ChatRequest):
```
{
  "tenantId": "string",
  "user": { "id": "string", "role": "string" },
  "threadId": "string",
  "uiContext": { "module": "string", "entityId": "string?", "route": "string?" },
  "message": "string",
  "caps": { "allowBenchmarks": boolean?, "allowWeb": boolean? }?,
  "response_format": "advisor_v1"
}
```

Streaming Frames (AdvisorFrame):
```
{
  "type": "start" | "chunk" | "tool" | "final" | "error",
  "content": "string?",
  "actions": [{ "label": "string", "href": "string" }]?,
  "sources": [{ "kind": "internal" | "external", "docId": "string", "location": "string?", "title": "string?", "url": "string?" }]?,
  "meta": {
    "correlationId": "string?",
    "tokens": { "prompt": number?, "completion": number? }?,
    "timing": { "ttfbMs": number? }?
  }?
}
```
Final MUST include actions[] and sources[] (arrays may be empty). SSE frames always include `meta.correlationId`.

## Architecture: Ports / Adapters / DI

- Ports (src/joyce_svc/ports/)
  - ToolsPort — file-backed data tools with strict Pydantic models:
    - KPITrendModel, ExecutiveSummaryModel, AlertsSummaryModel, EBITDAMarginModel, ValueLeakageModel
  - IntentClassifier — rule-based, LLM-based, or ensemble
  - MemoryPort — session memory (load/save/trim)
  - MetricsPort — noop by default
  - RateLimiterPort — in-proc per-identity limiter
- Adapters (src/joyce_svc/adapters/)
  - tools/file_backed.py — reads real JSON from DATA_ROOT (see Data Files below)
  - llm/rule_based.py, llm/anthropic_intent.py, llm/ensemble.py — classifiers
  - memory/inproc.py — in-process ring buffer with TTL
  - metrics/noop.py — noop metrics
  - rate_limit/inproc.py — per-key limiter (60s window)
- DI (src/joyce_svc/di.py)
  - get_tools() → FileBackedToolsAdapter(DATA_ROOT)
  - get_intent_classifier(), get_memory(), get_metrics(), get_rate_limiter()
  - Singletons via @lru_cache

## Data Files (required for real outputs)

The service reads company data from DATA_ROOT (default /data):

- /data/companies/{symbol}/era-report/executive-summary-dashboard.json
- /data/companies/{symbol}/executive-summary.json
- /data/companies/{symbol}/alerts.json
- /data/companies/{symbol}/era-report/value-leakage-financial-impact.json (optional; richer margin-driver actions)

Alternate schemas supported (in addition to the minimal examples below):
- KPI trends and EBITDA margin can be read from executive-summary-dashboard.json:
  - chartConfigurations.kpiTrends.data.labels/datasets (e.g., dataset label "EBITDA Margin %")
  - executiveKPIs[].metric == "EBITDA Margin"
- Alerts can be read when alerts.json contains an array of alert objects (with severity/category), not only grouped lists.
- Value leakage can be read from era-report/value-leakage-financial-impact.json:
  - leakageCategories[].category for areas
  - details.recoveryPath[] for mitigation strategies
  - valueLeakageOverview.totalLeakage.{percentage|amount} for estimated impact

Local data for development:
- Copy data into this repo (example already mirrored to dev-data/companies):
  - dev-data/companies/{symbol}/era-report/executive-summary-dashboard.json
  - dev-data/companies/{symbol}/era-report/value-leakage-financial-impact.json
  - dev-data/companies/{symbol}/executive-summary.json
  - dev-data/companies/{symbol}/alerts.json
- Run with DATA_ROOT pointing to that folder:
  - export AUTH_REQUIRED=false
  - export STREAM_FROM_GRAPH=true   # optional, to see tool frames
  - export DATA_ROOT="$(pwd)/dev-data"
  - uvicorn --app-dir src joyce_svc.main:app --host 127.0.0.1 --port 8080 --proxy-headers --log-level info
- SSE examples with local data:
  - curl -N -H "Content-Type: application/json" -H "Accept: text/event-stream" -X POST http://127.0.0.1:8080/v1/chat -d '{"tenantId":"CVS","user":{"id":"u1","role":"analyst"},"threadId":"t1","uiContext":{"module":"mirror","entityId":"kpi:ebitda-margin","route":"/digital-mirror?kpi=ebitda-margin"},"message":"What'\''s driving the gross margin change?","response_format":"advisor_v1"}'

Railway deployment:
- Mount a volume at /data and set DATA_ROOT to /data (or rely on default).
- The same file layout under /data/companies/{symbol}/... will be used.

Notes:
- Company symbols are resolved in lowercase on disk (e.g., companies/cvs), even if tenantId is "CVS" in requests.

Minimal examples:

executive-summary-dashboard.json:
```
{
  "kpis": {
    "ebitda-margin": { "current_value": 42.1, "previous_value": 41.8, "period": "Q3 2025", "unit": "%" }
  },
  "financial_metrics": {
    "ebitda_margin": { "current_margin": 42.1, "previous_margin": 41.8, "period": "Q3 2025" }
  },
  "value_leakage": {
    "areas": ["Supply Chain Inefficiencies"],
    "estimated_impact": "2.3% margin improvement potential",
    "mitigation_strategies": ["Optimize supplier contracts"],
    "priority": "high"
  }
}
```

executive-summary.json:
```
{ "key_highlights": ["Strong Q3 performance"], "risks": ["Labor inflation"], "overall_sentiment": "positive" }
```

alerts.json:
```
{ "alerts": { "high_priority": ["Supply chain optimization needed"], "operations": ["Traffic shift vs. historical"] } }
```

If files/keys are missing, the adapter returns graceful defaults (for continuity). When mounted data is present, real values are returned.

## Node Proxy Integration

The Node/Express app proxies to this service:
- POST /api/joyce/v1/chat → /v1/chat (SSE, advisor_v1)
- GET /api/joyce/health → /health

Proxy requirements:
- Preserve SSE headers (no buffering):
  - Content-Type: text/event-stream; charset=utf-8
- Forward headers:
  - Authorization: Bearer <jwt> (unaltered)
  - X-Correlation-Id (if provided; the service will also generate when absent)
  - Accept: text/event-stream (for /v1/chat)
- Forward JSON body unmodified; the body is authoritative for tenantId/user; JWT/body mismatches are logged.

## Environment Variables

Core:
- LOG_LEVEL=info
- ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,https://app-dev.rejoyce.ai,https://app-stg.rejoyce.ai,https://app.rejoyce.ai
- Optional: ALLOWED_ORIGIN_REGEX=^https://(app|app-stg|app-dev)\.rejoyce\.ai$
- AUTH_REQUIRED=true
- OIDC_ISSUER=…
- OIDC_AUDIENCE=…
- OIDC_JWKS_URL=… or OIDC_JWKS_PATH=…

Behavior & perf:
- ENABLE_UI_ACTIONS=true
- STREAM_FROM_GRAPH=false
- START_CHUNK_DELAY_MS=200
- REQUEST_TIMEOUT_S=6
- RATE_LIMIT_PER_MIN=60

Data:
- DATA_ROOT=/data

Memory (scaffold):
- MEMORY_ENABLED=false | true
- MEMORY_WINDOW=8
- MEMORY_TTL_MIN=30

LLM fallback (optional):
- JOYCE_LLM_CLASSIFIER=false | true
- INTENT_LLM_MODEL=claude-sonnet-4-20250514
- ANTHROPIC_API_KEY=… (required if JOYCE_LLM_CLASSIFIER=true)

Consultant persona (optional; default deterministic, LLM off):
- CONSULTANT_VOICE_ENABLED=true | false     # default: true
- CONSULTANT_VOICE_LLM_ENABLED=false | true # default: false
- CONSULTANT_VOICE_MODEL=claude-sonnet-4-20250514 | gpt-4o-mini | ...
- CONSULTANT_VOICE_TIMEOUT_MS=800
- ANTHROPIC_API_KEY=…   # required if CONSULTANT_VOICE_MODEL starts with "claude"
- OPENAI_API_KEY=…      # required if using OpenAI/ChatOpenAI models (e.g., gpt-4o-mini)
- Notes:
  - When CONSULTANT_VOICE_MODEL is unset, it defaults to INTENT_LLM_MODEL.
  - LLM refinement never changes factual numbers; on timeout/error it falls back to deterministic wrapping.

## Workflow Configuration

The service can be configured to run in two primary modes: a deterministic, rule-based workflow, or an LLM-driven workflow.

### Deterministic Workflow (Default)

In this mode, the service uses rule-based intent classification and a deterministic "consultant voice" for responses. This is the default behavior. To ensure you are running in this mode, set the following environment variables:

- `JOYCE_LLM_CLASSIFIER=false`
- `CONSULTANT_VOICE_LLM_ENABLED=false`

### LLM-Driven Workflow

In this mode, the service uses an LLM for intent classification and for generating the final "consultant voice" response. To enable this mode, set the following environment variables:

- `JOYCE_LLM_CLASSIFIER=true`
- `CONSULTANT_VOICE_LLM_ENABLED=true`
- `ANTHROPIC_API_KEY=your-api-key` (or `OPENAI_API_KEY` if using an OpenAI model)

You can also mix and match these settings. For example, you could use a rule-based classifier with an LLM-generated voice.

Metrics:
- METRICS_ENABLED=false

## Local Development

Prereqs: Python 3.11+

Install:
```
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

Environment setup:
```
cp .env.example .env
# Edit values as needed (CORS, AUTH_REQUIRED, DATA_ROOT, etc.)
# Never commit real secrets. For production/staging, set variables in the Railway UI.
```

Run service (choose one):

Option A (recommended, adds src automatically):
```
export PORT=8080
uvicorn --app-dir src joyce_svc.main:app --host 0.0.0.0 --port ${PORT:-8080} --proxy-headers --log-level info
```

Option B (explicit path):
```
export PORT=8080
PYTHONPATH=src uvicorn joyce_svc.main:app --host 0.0.0.0 --port ${PORT:-8080} --proxy-headers --log-level info
```

OpenAPI docs: http://localhost:8080/docs

## SSE Usage (cURL)

Health:
```
curl -s http://localhost:8080/health | jq
```

Unauthorized (AUTH_REQUIRED=true):
```
curl -N -H "Content-Type: application/json" -X POST http://localhost:8080/v1/chat -d '{
  "tenantId":"t_abc",
  "user":{"id":"u_123","role":"Investor"},
  "threadId":"th_789",
  "uiContext":{"module":"mirror","entityId":"kpi:ebitda-margin","route":"/digital-mirror?kpi=ebitda-margin"},
  "message":"Give me a 5-bullet investor brief for this month.",
  "response_format":"advisor_v1"
}'
# Expect one error frame then close
```

Happy path (DEV only):
```
export AUTH_REQUIRED=false
curl -N -H "Content-Type: application/json" \
  -H "X-Correlation-Id: 11111111-1111-4111-8111-111111111111" \
  -X POST http://localhost:8080/v1/chat -d '{
    "tenantId":"CVS",
    "user":{"id":"u_1","role":"analyst"},
    "threadId":"thread-1",
    "uiContext":{"module":"mirror","entityId":"kpi:ebitda-margin","route":"/digital-mirror?kpi=ebitda-margin"},
    "message":"Give me a 5-bullet investor brief",
    "response_format":"advisor_v1"
  }'
```

## Testing

Pytest with asyncio support. No external services required.

### Test Organization

Tests are organized by type:
- `tests/unit/` - Unit tests for individual components
- `tests/integration/` - Integration tests for component interactions
- `tests/api/` - API endpoint tests including SSE behavior

### Running Tests

Run all tests:
```
pytest -q
```

Run by category:
```
pytest -q tests/unit
pytest -q tests/integration
pytest -q tests/api
```

Run specific test file:
```
pytest -q tests/api/test_chat_sse.py
```

Run with verbose output:
```
pytest -v
```

### Test Coverage

Current test suite includes:
- **Unit tests** (tests/unit/):
  - Schema validation (advisor_v1 contract)
  - Intent classifiers (rule-based, ensemble)
  - Middleware components (correlation ID, rate limiting)
  - Evidence recording and deduplication
  - File-backed tools adapter
  - Memory store operations
  - Graph intent classification

- **Integration tests** (tests/integration/):
  - LangGraph flow execution (investor snapshot, margin driver, execution proposal)
  - Dependency injection wiring and singleton behavior
  - Memory persistence across invocations
  - Critical path scenarios
  - System refactoring safety

- **API tests** (tests/api/):
  - SSE streaming behavior and frame ordering
  - Error handling (auth failures, rate limiting, validation)
  - CORS header validation
  - Health endpoint
  - Chat SSE contract compliance

### Known Test Issues

Some tests may need adjustments based on implementation details:
- Rate limiter tests may be affected by global state
- Protocol type checking in Python 3.13 requires @runtime_checkable decorator
- Graph state returns Pydantic models, not dicts

## Deployment to Railway

A full deployment guide (env vars, volumes, proxy integration, smoke tests) is provided in:
- RAILWAY.md

Key steps:
- Connect Railway to this repo; it autodetects Dockerfile.
- Set env vars (see sections above).
- Mount a Volume at /data and populate company files (or bake demo data for non-prod).
- Configure health check at /health.
- In Node, set JOYCE_SERVICE_URL to the deployed service and forward SSE headers without buffering.

## Rate Limiting, Timing, and Logs

- In-proc per-identity limiter (RATE_LIMIT_PER_MIN).
- HttpTimingMiddleware logs structured `http_timing` entries (elapsed_ms).
- All logs include correlationId for tracing.
- AdvisorFrame.meta.timing.ttfbMs populated for first chunk.

## Project Layout

- src/joyce_svc/main.py — FastAPI app wiring, middleware, routers
- src/joyce_svc/routes/health.py — GET /health
- src/joyce_svc/routes/chat.py — POST /v1/chat (SSE)
- src/joyce_svc/agent/graph.py — LangGraph wiring
- src/joyce_svc/agent/nodes.py — graph nodes (deterministic; DI-backed tools and memory)
- src/joyce_svc/agent/flows/* — per-flow composition
- src/joyce_svc/models/schemas.py — ChatRequest, AdvisorFrame, Action, SourceRef
- src/joyce_svc/middleware/* — correlation, auth, http timing, rate limiting
- src/joyce_svc/ports/* — ports/interfaces
- src/joyce_svc/adapters/* — concrete adapter implementations
- src/joyce_svc/config.py — env and settings
- src/joyce_svc/logging.py — structured JSON logging setup
- src/joyce_svc/errors.py — error taxonomy
- tests/ — comprehensive test suite organized by type (unit/integration/api)

## Features

- Streaming advisor_v1 SSE API with start → chunk → final frames (and error handling)
- Deterministic agent orchestration (LangGraph + typed state)
- Consultant voice persona (default): deterministic wrapper adds 5-section consultant structure (ack/validation, key insights, evidence with citations, risks, next steps). Optional LLM refinement is provider-aware (Claude via Anthropic SDK, non-Claude via OpenAI/ChatOpenAI), timeboxed, and falls back safely.
- Evidence and citations with stable docIds (see src/joyce_svc/evidence/doc_registry.py)
- Deep links generated from UI context
- Optional session memory (in-process) for turn history
- Auth via JWT/OIDC (configurable), correlation IDs, structured logs
- File-backed data adapters for local/demo data; graceful defaults when data is missing

### Supported Demo Flows (MVP)
- Investor snapshot brief (5-bullet summary with disclosure and deep link)
- Margin driver analysis (drivers and suggested actions)
- Execution proposal (owners and timelines)

## Why This Project Is Useful

- Produces structured, evidence-backed answers suitable for enterprise users
- Enforces a stable streaming contract for front-end integration
- Clear separation of concerns (ports/adapters/DI) for maintainability and testability
- Deterministic behavior by default; easy to introduce LLMs behind ports when needed

## Getting Help

- Open a GitHub issue: https://github.com/RejoyceAI/rejoyce-joyce-agent/issues
- For authentication or deployment questions, see Environment Variables and Deployment sections
- For streaming contract details, see Advisor_v1 Contract section

## Maintainers and Contributions

- Maintained by RejoyceAI Engineering
- Contributions welcome via pull requests
  - Follow existing code style (typed state, Pydantic models, deterministic nodes)
  - Include unit and integration tests for new functionality
  - Describe changes clearly and reference any related issues

- advisor_v1 contract is stable and enforced by Pydantic models and route behavior.
- Evidence docIds are stable constants in MVP; a more granular evidence service can be added later without changing the advisor_v1 interface.
