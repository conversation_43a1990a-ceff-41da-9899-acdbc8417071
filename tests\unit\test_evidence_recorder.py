"""Unit tests for evidence recording and deduplication."""

import pytest
from joyce_svc.adapters.evidence.basic import EvidenceRecorder
from joyce_svc.models.schemas import SourceRef


def test_evidence_recorder_deduplication():
    """EvidenceRecorder should deduplicate docIds."""
    recorder = EvidenceRecorder()
    
    # Record same docId multiple times
    recorder.record(doc_id="DOC_1")
    recorder.record(doc_id="DOC_1")
    recorder.record(doc_id="DOC_1", location="section-2")
    
    sources = recorder.sources()
    
    # Dedup is by (docId, location). Two unique entries expected: (DOC_1, None) and (DOC_1, "section-2")
    assert len(sources) == 2
    doc_pairs = {(s.docId, s.location) for s in sources}
    assert ("DOC_1", None) in doc_pairs
    assert ("DOC_1", "section-2") in doc_pairs


def test_evidence_recorder_preserves_order():
    """EvidenceRecorder should preserve order of unique docIds."""
    recorder = EvidenceRecorder()
    
    recorder.record(doc_id="DOC_A")
    recorder.record(doc_id="DOC_B")
    recorder.record(doc_id="DOC_C")
    recorder.record(doc_id="DOC_B")  # Duplicate, should not add
    
    sources = recorder.sources()
    
    assert len(sources) == 3
    assert sources[0].docId == "DOC_A"
    assert sources[1].docId == "DOC_B"
    assert sources[2].docId == "DOC_C"


def test_evidence_recorder_with_seed():
    """EvidenceRecorder should accept seed sources."""
    existing = [
        SourceRef(kind="internal", docId="EXISTING_1"),
        SourceRef(kind="internal", docId="EXISTING_2"),
    ]
    
    recorder = EvidenceRecorder(seed=existing)
    
    # Add new sources
    recorder.record(doc_id="NEW_1")
    recorder.record(doc_id="EXISTING_1")  # Duplicate of seed
    
    sources = recorder.sources()
    
    # Should have 3 unique sources
    assert len(sources) == 3
    doc_ids = {s.docId for s in sources}
    assert doc_ids == {"EXISTING_1", "EXISTING_2", "NEW_1"}


def test_evidence_recorder_location_tracking():
    """EvidenceRecorder should track location when provided."""
    recorder = EvidenceRecorder()
    
    recorder.record(doc_id="DOC_1", location="page-5")
    recorder.record(doc_id="DOC_2", location="section-3.2")
    recorder.record(doc_id="DOC_3")  # No location
    
    sources = recorder.sources()
    
    assert sources[0].location == "page-5"
    assert sources[1].location == "section-3.2"
    assert sources[2].location is None


def test_evidence_recorder_with_title():
    """EvidenceRecorder should include title when provided."""
    recorder = EvidenceRecorder()
    
    recorder.record(doc_id="DOC_1", title="Executive Summary")
    recorder.record(doc_id="DOC_2", title="Q3 Report")
    
    sources = recorder.sources()
    
    assert sources[0].title == "Executive Summary"
    assert sources[1].title == "Q3 Report"


def test_evidence_recorder_external_sources():
    """EvidenceRecorder should handle external sources."""
    recorder = EvidenceRecorder()
    
    recorder.record(
        doc_id="EXT_1",
        kind="external",
        title="Industry Report",
        url="https://example.com/report"
    )
    recorder.record(doc_id="INT_1", kind="internal")
    
    sources = recorder.sources()
    
    assert sources[0].kind == "external"
    assert sources[0].url == "https://example.com/report"
    assert sources[1].kind == "internal"
    assert sources[1].url is None


def test_evidence_recorder_empty():
    """EvidenceRecorder should handle empty state."""
    recorder = EvidenceRecorder()
    sources = recorder.sources()
    
    assert sources == []
    assert isinstance(sources, list)


def test_evidence_recorder_sources_copy():
    """EvidenceRecorder.sources() should return a copy, not a live list."""
    recorder = EvidenceRecorder()
    recorder.record(doc_id="DOC_1")
    original = recorder.sources()
    # Mutate the returned list
    original.append(SourceRef(kind="internal", docId="DOC_X"))
    # Internal state should remain unchanged
    fresh = recorder.sources()
    assert len(fresh) == 1
    assert fresh[0].docId == "DOC_1"
