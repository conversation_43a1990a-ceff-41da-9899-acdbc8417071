from __future__ import annotations

class AppError(Exception):
    """Base application error (not directly exposed to clients)."""


class AuthError(AppError):
    """Authentication/authorization related error."""


class ToolError(AppError):
    """Raised when a tool adapter fails or returns invalid data."""


class TimeoutError(AppError):
    """Raised when a request or graph run exceeds configured timeout."""


class ProviderError(AppError):
    """Raised when an upstream provider (LLM, HTTP) fails."""


class ValidationError(AppError):
    """Raised when inputs or model validations fail in a recoverable way."""
