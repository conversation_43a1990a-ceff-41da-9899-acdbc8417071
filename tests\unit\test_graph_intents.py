import pytest
from joyce_svc.adapters.llm.rule_based import RuleBasedClassifier

from joyce_svc.agent.graph import get_graph_app


def _invoke(query: str, tenant: str = "t_abc"):
    app = get_graph_app()
    initial_state = {
        "query": query,
        "intent": None,
        "context": {
            "tenantId": tenant,
            "threadId": "th_1",
            "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin", "route": "/digital-mirror?kpi=ebitda-margin"},
            "user": {"id": "u_1", "role": "Investor"},
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": [],
    }
    return app.invoke(initial_state)


def test_intent_investor_snapshot_routing():
    final_state = _invoke("Give me a 5-bullet investor brief for this month.")
    assert final_state.intent == "investor_snapshot"
    assert isinstance(final_state.response, str) and len(final_state.response) > 0
    assert isinstance(final_state.actions, list)


def test_intent_margin_driver_routing():
    final_state = _invoke("What's driving the gross-margin change?")
    assert final_state.intent == "margin_driver"
    assert isinstance(final_state.response, str) and len(final_state.response) > 0
    assert isinstance(final_state.actions, list)


def test_intent_execution_proposal_routing():
    final_state = _invoke("Propose owners and timelines to address margin.")
    assert final_state.intent == "execution_proposal"
    assert isinstance(final_state.response, str) and len(final_state.response) > 0
    assert isinstance(final_state.actions, list)


def test_fallback_clarify_question():
    final_state = _invoke("Hello there, how are you?")
    # In fallback path we keep intent None and provide a clarifying response
    assert final_state.intent is None
    assert "clarify" in final_state.response.lower() or len(final_state.response) > 0
    assert isinstance(final_state.actions, list)


@pytest.mark.parametrize(
    "query, expected_intent",
    [
        ("Give me a 5-bullet investor brief", "investor_snapshot"),
        ("show me a summary for investors", "investor_snapshot"),
        ("What's driving the gross-margin change?", "margin_driver"),
        ("why did margins change", None),
        ("Propose owners and timelines to address margin", "execution_proposal"),
        ("who can fix the margin issue", None),
        ("Hello there, how are you?", None),
    ],
)
def test_rule_based_intent_classifier(query, expected_intent):
    classifier = RuleBasedClassifier()
    result = classifier.classify(text=query)
    assert result["intent"] == expected_intent
