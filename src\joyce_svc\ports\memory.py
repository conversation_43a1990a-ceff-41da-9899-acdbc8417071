from __future__ import annotations

from typing import Protocol, List, Dict, Any, Optional
from pydantic import BaseModel, ConfigDict, Field


class MemoryTurn(BaseModel):
    model_config = ConfigDict(extra="forbid")
    role: str = Field(..., description="user|assistant|system")
    content: str = Field(..., description="message text")
    meta: Dict[str, Any] = Field(default_factory=dict)


class MemorySnapshot(BaseModel):
    model_config = ConfigDict(extra="forbid")
    thread_id: str
    turns: List[MemoryTurn] = Field(default_factory=list)
    ui_context: Dict[str, Any] = Field(default_factory=dict)
    user_role: Optional[str] = None


class MemoryPort(Protocol):
    """
    Session memory port. Implementations can be in-proc, Redis-backed, etc.
    """
    def load(self, *, thread_id: str) -> MemorySnapshot: ...
    def save(self, *, thread_id: str, turn: MemoryTurn, ui_context: Optional[Dict[str, Any]] = None, user_role: Optional[str] = None) -> None: ...
    def trim(self, *, thread_id: str, window: int) -> None: ...
