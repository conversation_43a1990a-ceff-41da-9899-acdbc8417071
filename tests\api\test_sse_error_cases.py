"""API tests for SSE error cases."""

import json
import pytest
from starlette.testclient import TestClient
from joyce_svc.main import app
from joyce_svc.config import settings


def test_sse_auth_failure_emits_error_frame(monkeypatch):
    """Auth failure should emit single error frame on SSE endpoint."""
    # Enable auth requirement
    monkeypatch.setattr(settings, "AUTH_REQUIRED", True)
    
    client = TestClient(app)
    
    # Request without Authorization header
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test message",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200  # SSE always returns 200
        
        # Read SSE lines
        lines = []
        for line in resp.iter_lines():
            if line:
                lines.append(line)
            if len(lines) > 3:  # Should only get one error frame
                break
        
        # Find data line
        data_lines = [l for l in lines if l.startswith("data: ")]
        assert len(data_lines) >= 1
        
        # Parse first frame
        frame_data = json.loads(data_lines[0].replace("data: ", ""))
        assert frame_data["type"] == "error"
        assert frame_data["content"] == "Unauthorized"
        assert "correlationId" in frame_data.get("meta", {})


def test_sse_invalid_json_body(monkeypatch):
    """Invalid JSON body should return 422."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    # Invalid request (missing required fields)
    req = {
        "message": "Test",
        # Missing tenantId, user, threadId, etc.
    }
    
    response = client.post(
        "/v1/chat",
        json=req,
        headers={"Accept": "text/event-stream"}
    )
    
    assert response.status_code == 422
    assert "validation" in response.text.lower() or "field required" in response.text.lower()


def test_sse_invalid_response_format(monkeypatch):
    """Invalid response_format should return 422."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "invalid_format",  # Should be 'advisor_v1'
    }
    
    response = client.post(
        "/v1/chat",
        json=req,
        headers={"Accept": "text/event-stream"}
    )
    
    assert response.status_code == 422


def test_sse_rate_limiting(monkeypatch):
    """Rate limiting should return 429 after limit exceeded."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    monkeypatch.setattr(settings, "RATE_LIMIT_PER_MIN", 2)  # Very low limit
    
    # Clear rate limiter cache
    from joyce_svc import di
    if hasattr(di.get_rate_limiter, "cache_clear"):
        di.get_rate_limiter.cache_clear()
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
    }
    
    # First two requests should succeed
    for i in range(2):
        with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
            assert resp.status_code == 200
    
    # Third request should be rate limited
    response = client.post(
        "/v1/chat",
        json=req,
        headers={"Accept": "text/event-stream"}
    )
    assert response.status_code == 429
    assert "Too Many Requests" in response.text


def test_sse_timeout_during_graph_execution(monkeypatch):
    """Timeout during graph execution should emit error frame."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    monkeypatch.setattr(settings, "REQUEST_TIMEOUT_S", 0.001)  # Very short timeout
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Give me an investor brief",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect all frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # Should have error frame due to timeout
        error_frames = [f for f in frames if f["type"] == "error"]
        assert len(error_frames) > 0
        assert "timeout" in error_frames[0]["content"].lower()


def test_sse_missing_accept_header(monkeypatch):
    """Missing Accept header should still work (default behavior)."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
    }
    
    # No Accept header
    with client.stream("POST", "/v1/chat", json=req) as resp:
        assert resp.status_code == 200
        assert resp.headers["content-type"].startswith("text/event-stream")


def test_sse_extra_fields_forbidden(monkeypatch):
    """Extra fields in request should be rejected."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
        "extra_field": "should_not_be_allowed",  # Extra field
    }
    
    response = client.post(
        "/v1/chat",
        json=req,
        headers={"Accept": "text/event-stream"}
    )
    
    assert response.status_code == 422
    assert "extra" in response.text.lower()
