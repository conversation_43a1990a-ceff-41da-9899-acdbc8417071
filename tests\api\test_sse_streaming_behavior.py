"""API tests for SSE streaming behavior."""

import json
import pytest
from starlette.testclient import TestClient
from joyce_svc.main import app
from joyce_svc.config import settings


def test_sse_frames_arrive_in_correct_order(monkeypatch):
    """SSE frames should arrive in order: start → chunk → final."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test message",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # Verify we have frames
        assert len(frames) >= 3
        
        # Verify order
        assert frames[0]["type"] == "start"
        
        # Middle frames should be chunks or tools
        middle_types = [f["type"] for f in frames[1:-1]]
        for t in middle_types:
            assert t in ["chunk", "tool"]
        
        # Last frame should be final
        assert frames[-1]["type"] == "final"


def test_sse_correlation_id_consistency(monkeypatch):
    """All frames should have consistent correlationId."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    correlation_id = "11111111-1111-4111-8111-111111111111"
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
    }
    
    headers = {
        "Accept": "text/event-stream",
        "X-Correlation-Id": correlation_id,
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers=headers) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # All frames should have the same correlationId
        for frame in frames:
            assert frame.get("meta", {}).get("correlationId") == correlation_id


def test_sse_ttfb_present_on_first_chunk(monkeypatch):
    """ttfbMs should be present on first chunk frame."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # Find first chunk frame
        chunk_frames = [f for f in frames if f["type"] == "chunk"]
        assert len(chunk_frames) > 0
        
        # First chunk should have ttfbMs
        first_chunk = chunk_frames[0]
        assert "meta" in first_chunk
        assert "timing" in first_chunk["meta"]
        assert "ttfbMs" in first_chunk["meta"]["timing"]
        assert isinstance(first_chunk["meta"]["timing"]["ttfbMs"], int)
        assert first_chunk["meta"]["timing"]["ttfbMs"] > 0


def test_sse_tool_frames_when_enabled(monkeypatch):
    """Tool frames should appear when STREAM_FROM_GRAPH=true."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    monkeypatch.setattr(settings, "STREAM_FROM_GRAPH", True)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Give me an investor brief",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # Should have tool frames
        tool_frames = [f for f in frames if f["type"] == "tool"]
        assert len(tool_frames) > 0
        
        # Tool frames should have content
        for tf in tool_frames:
            assert "content" in tf
            assert "graph" in tf["content"].lower() or "agent" in tf["content"].lower()


def test_sse_final_frame_has_required_fields(monkeypatch):
    """Final frame must have actions and sources arrays."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # Find final frame
        final_frames = [f for f in frames if f["type"] == "final"]
        assert len(final_frames) == 1
        
        final = final_frames[0]
        
        # Must have content
        assert "content" in final
        assert isinstance(final["content"], str)
        
        # Must have actions array (can be empty)
        assert "actions" in final
        assert isinstance(final["actions"], list)
        
        # Must have sources array (can be empty)
        assert "sources" in final
        assert isinstance(final["sources"], list)
        
        # Must have meta with correlationId
        assert "meta" in final
        assert "correlationId" in final["meta"]


def test_sse_content_type_header(monkeypatch):
    """Response should have correct SSE content-type."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Check content-type
        content_type = resp.headers.get("content-type", "")
        assert "text/event-stream" in content_type
        assert "charset=utf-8" in content_type


def test_sse_correlation_id_generated_when_missing(monkeypatch):
    """CorrelationId should be generated when not provided."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-1",
        "uiContext": {"module": "mirror"},
        "message": "Test",
        "response_format": "advisor_v1",
    }
    
    # No X-Correlation-Id header
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # All frames should have a correlationId
        correlation_ids = set()
        for frame in frames:
            cid = frame.get("meta", {}).get("correlationId")
            assert cid is not None
            correlation_ids.add(cid)
        
        # Should be the same ID across all frames
        assert len(correlation_ids) == 1
        
        # Should be a valid UUID format
        import uuid
        cid = correlation_ids.pop()
        uuid.UUID(cid)  # Will raise if invalid


def test_sse_final_frame_has_consultant_sections_by_default(monkeypatch):
    """Verify consultant structure appears in SSE final frame by default."""
    monkeypatch.setattr(settings, "AUTH_REQUIRED", False)
    # Ensure consultant voice is enabled (should be default)
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_ENABLED", True)
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_LLM_ENABLED", False)  # Use deterministic
    
    client = TestClient(app)
    
    req = {
        "tenantId": "CVS",
        "user": {"id": "u1", "role": "analyst"},
        "threadId": "thread-consultant-1",
        "uiContext": {"module": "mirror"},
        "message": "Give me an investor brief",
        "response_format": "advisor_v1",
    }
    
    with client.stream("POST", "/v1/chat", json=req, headers={"Accept": "text/event-stream"}) as resp:
        assert resp.status_code == 200
        
        # Collect frames
        frames = []
        for line in resp.iter_lines():
            if line and line.startswith("data: "):
                frame_data = json.loads(line.replace("data: ", ""))
                frames.append(frame_data)
        
        # Find final frame
        final_frames = [f for f in frames if f["type"] == "final"]
        assert len(final_frames) == 1
        
        final = final_frames[0]
        content = final["content"]
        
        # Verify consultant sections present
        assert "Acknowledgment:" in content
        assert "Key insights:" in content
        assert "Evidence:" in content
        assert "Risks:" in content
        assert "Next steps:" in content
        
        # Verify humble inquiry
        assert "Does this align" in content or "objective" in content.lower()
