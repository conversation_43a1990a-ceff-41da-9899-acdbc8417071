from __future__ import annotations

from typing import Optional

from joyce_svc.ports.llm import IntentClassifier, ClassificationResult, Intent


class RuleBasedClassifier(IntentClassifier):
    """
    Deterministic, zero-temp style classifier using simple keyword cues.
    Mirrors current classify_intent_node rule logic.
    """

    def classify(self, text: str) -> ClassificationResult:
        t = (text or "").lower()

        def has(*words: str) -> bool:
            return all(w in t for w in words)

        # Investor brief signals
        if has("investor") or has("brief") or has("5-bullet") or has("5 bullet"):
            return ClassificationResult(intent="investor_snapshot", confidence=1.0)  # type: ignore[typeddict-item]

        # Margin driver analysis
        if (has("margin") and has("driver")) or "what's driving" in t or "whats driving" in t or "what is driving" in t:
            return ClassificationResult(intent="margin_driver", confidence=1.0)  # type: ignore[typeddict-item]

        # Execution proposal
        if "execution" in t or "proposal" in t or ("owners" in t and "timeline" in t):
            return ClassificationResult(intent="execution_proposal", confidence=1.0)  # type: ignore[typeddict-item]

        return ClassificationResult(intent=None, confidence=0.0)
