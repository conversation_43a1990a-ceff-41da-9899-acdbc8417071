from __future__ import annotations

from typing import List

from pydantic import BaseModel, ConfigDict, Field

from joyce_svc.models.schemas import Action


class FlowOutput(BaseModel):
    model_config = ConfigDict(extra="forbid")

    content: str = Field(..., description="Formatted response text for the flow")
    actions: List[Action] = Field(default_factory=list, description="Actions for the UI to render")


class InvestorBriefModel(FlowOutput):
    """Typed output for Investor Snapshot flow (kept simple for MVP refactor)."""
    pass


class DriverAnalysisModel(FlowOutput):
    """Typed output for Margin Driver flow (kept simple for MVP refactor)."""
    pass


class ExecutionProposalModel(FlowOutput):
    """Typed output for Execution Proposal flow (kept simple for MVP refactor)."""
    pass
