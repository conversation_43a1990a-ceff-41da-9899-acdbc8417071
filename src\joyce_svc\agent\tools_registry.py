from __future__ import annotations

from typing import Any, Dict, List, Optional, TypedDict

from langchain.tools import tool
from joyce_svc.models.schemas import SourceRef
from joyce_svc.di import get_tools


class KPITrend(TypedDict):
    kpi: str
    points: List[Dict[str, Any]]  # [{"date":"2025-08","value":...}, ...]


class ExecutiveSummary(TypedDict):
    highlights: List[str]
    risks: List[str]
    cta: str


class AlertsSummary(TypedDict):
    categories: List[str]
    top_alerts: List[str]


class EBITDAMargin(TypedDict):
    current_margin: float
    previous_margin: Optional[float]
    trend: str
    period: str


class ValueLeakage(TypedDict):
    areas: List[str]
    estimated_impact: Optional[str]
    mitigation_strategies: List[str]
    priority: str


# DocId registry moved to centralized module
from joyce_svc.evidence.doc_registry import DOC_MIRROR_TRENDS, DOC_SEI_EXEC_SUM, DOC_ALERTS  # noqa: E402


@tool
def fetch_kpi_trends(company_symbol: str, kpi_name: str) -> KPITrend:
    """
    Fetch KPI trend data from the Digital Mirror.
    Delegates to DI-provided ToolsPort adapter (file-backed when data available, else fallback).
    """
    tools = get_tools()
    model = tools.fetch_kpi_trends(company_symbol=company_symbol, kpi_name=kpi_name)
    return {
        "kpi": model.kpi,
        "points": list(model.points),
    }


@tool
def fetch_executive_summary(company_symbol: str) -> ExecutiveSummary:
    """
    Fetch SEI executive summary.
    Delegates to DI-provided ToolsPort adapter.
    """
    tools = get_tools()
    model = tools.fetch_executive_summary(company_symbol=company_symbol)
    return {
        "highlights": list(model.highlights),
        "risks": list(model.risks),
        "cta": model.cta,
    }


@tool
def fetch_alerts_summary(company_symbol: str) -> AlertsSummary:
    """
    Fetch recent alerts summary.
    Delegates to DI-provided ToolsPort adapter.
    """
    tools = get_tools()
    model = tools.fetch_alerts_summary(company_symbol=company_symbol)
    return {
        "categories": list(model.categories),
        "top_alerts": list(model.top_alerts),
    }


@tool
def fetch_ebitda_margin(company_symbol: str) -> EBITDAMargin:
    """
    Fetch EBITDA margin summary from the Digital Mirror.
    Delegates to DI-provided ToolsPort adapter.
    """
    tools = get_tools()
    model = tools.fetch_ebitda_margin(company_symbol=company_symbol)
    return {
        "current_margin": model.current_margin,
        "previous_margin": model.previous_margin,
        "trend": model.trend,
        "period": model.period,
    }


@tool
def fetch_value_leakage(company_symbol: str) -> ValueLeakage:
    """
    Fetch value leakage analysis from the Digital Mirror.
    Delegates to DI-provided ToolsPort adapter.
    """
    tools = get_tools()
    model = tools.fetch_value_leakage(company_symbol=company_symbol)
    return {
        "areas": list(model.areas),
        "estimated_impact": model.estimated_impact,
        "mitigation_strategies": list(model.mitigation_strategies),
        "priority": model.priority,
    }


def evidence_from_tools(include_sei: bool = True, include_mirror: bool = True, include_alerts: bool = False) -> List[SourceRef]:
    """
    Construct minimal SourceRef list from the tools accessed.
    """
    sources: List[SourceRef] = []
    if include_mirror:
        sources.append(SourceRef(kind="internal", docId=DOC_MIRROR_TRENDS, title="KPI Trends"))
    if include_sei:
        sources.append(SourceRef(kind="internal", docId=DOC_SEI_EXEC_SUM, title="Executive Summary"))
    if include_alerts:
        sources.append(SourceRef(kind="internal", docId=DOC_ALERTS, title="Alerts Summary"))
    return sources
