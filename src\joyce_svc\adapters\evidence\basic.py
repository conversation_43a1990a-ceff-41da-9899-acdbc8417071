from __future__ import annotations

from typing import List, Optional, Set, Tuple

from joyce_svc.models.schemas import SourceRef
from joyce_svc.evidence.doc_registry import title_for_doc


class EvidenceRecorder:
    """
    Minimal in-memory evidence recorder for a single request/graph run.

    - Records SourceRef entries with stable docIds and optional location/url/title.
    - Deduplicates on (docId, location) to avoid repeated sources.
    - Can be seeded with existing sources[] from state and produce a merged, deduped list.
    """

    def __init__(self, seed: Optional[List[SourceRef]] = None) -> None:
        self._items: List[SourceRef] = []
        self._seen: Set[Tuple[str, Optional[str]]] = set()
        if seed:
            for s in seed:
                key = (s.docId, s.location)
                if key not in self._seen:
                    self._items.append(s)
                    self._seen.add(key)

    def record(
        self,
        *,
        doc_id: str,
        kind: str = "internal",
        title: Optional[str] = None,
        location: Optional[str] = None,
        url: Optional[str] = None,
    ) -> None:
        # Default title from registry if not provided
        title = title or title_for_doc(doc_id)
        key = (doc_id, location)
        if key in self._seen:
            return
        self._items.append(
            SourceRef(kind=kind, docId=doc_id, location=location, title=title, url=url)
        )
        self._seen.add(key)

    def sources(self) -> List[SourceRef]:
        # Return a copy to avoid accidental external mutations
        return list(self._items)
