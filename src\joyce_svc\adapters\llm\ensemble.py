from __future__ import annotations

from typing import Optional

from joyce_svc.ports.llm import IntentClassifier, ClassificationResult


class EnsembleClassifier(IntentClassifier):
    """
    Rule-first ensemble with optional LLM fallback.
    - Calls primary.classify(text) first.
    - If primary returns None/low confidence, calls fallback.classify(text).
    - Safe defaults to {intent: None, confidence: 0.0} on any error.
    """

    def __init__(self, *, primary: IntentClassifier, fallback: Optional[IntentClassifier] = None, min_confidence: float = 0.5) -> None:
        self._primary = primary
        self._fallback = fallback
        self._min_confidence = float(min_confidence)

    def classify(self, text: str) -> ClassificationResult:
        try:
            r1 = self._primary.classify(text)
            if r1.get("intent") is not None and (r1.get("confidence") or 0.0) >= self._min_confidence:
                return r1
            if self._fallback is not None:
                r2 = self._fallback.classify(text)
                return r2
        except Exception:
            pass
        return ClassificationResult(intent=None, confidence=0.0)
