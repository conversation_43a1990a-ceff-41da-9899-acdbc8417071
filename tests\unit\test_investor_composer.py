import re

from joyce_svc.agent.flows.investor_snapshot.composer import compose_investor_snapshot


def test_compose_investor_snapshot_builds_5_bullets_and_action():
    ctx = {
        "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin"},
        "mirror": {
            "trends": {
                "points": [
                    {"date": "2025-07", "value": 41.8},
                    {"date": "2025-08", "value": 42.1},
                ]
            },
            "ebitda_margin": {
                "current_margin": 42.1,
                "previous_margin": 41.8,
                "trend": "up",
                "period": "Q3 2025",
            },
        },
        "alerts": {
            "top_alerts": ["Margin compression in Region A", "Inventory buildup"],
        },
        "sei": {
            "risks": ["Supply chain pressures", "Regulatory headwinds"],
            "cta": "Review Executive Overview for deeper context and recommendations",
        },
    }

    out = compose_investor_snapshot(ctx, [])

    # 5 numbered bullets
    lines = out.content.splitlines()
    assert len(lines) == 5
    for i, line in enumerate(lines, start=1):
        assert line.startswith(f"{i}) ")

    # Must include EBITDA proxy disclosure
    assert "Gross margin is proxied by EBITDA margin" in out.content

    # Must provide the deep link action
    assert len(out.actions) >= 1
    first = out.actions[0]
    assert first.label == "Open Executive Overview"
    assert first.href == "/sei-report/executive-summary"
