from __future__ import annotations

from typing import Any, Dict, List

from joyce_svc.adapters.links.basic import DeepLinkBuilder
from joyce_svc.models.flows import DriverAnalysisModel
from joyce_svc.models.schemas import Action, SourceRef


def _safe_str_items(items, limit: int = 3) -> List[str]:
    out: List[str] = []
    if isinstance(items, list):
        for it in items:
            if isinstance(it, (str, int, float)):
                out.append(str(it))
            if len(out) >= limit:
                break
    return out


def _derive_summary(ctx: Dict[str, Any]) -> str:
    # Prefer precomputed analysis.summary from analyze_margin_node
    analysis = (ctx or {}).get("analysis") or {}
    summary = analysis.get("summary")
    if isinstance(summary, str) and summary.strip():
        return summary.strip()

    # Fallback: derive basic clause from mirror.ebitda_margin
    margin = ((ctx or {}).get("mirror") or {}).get("ebitda_margin") or {}
    trend = margin.get("trend", "stable")
    cm = margin.get("current_margin")
    pm = margin.get("previous_margin")
    period = margin.get("period", "current period")

    clause = f"EBITDA margin {trend}"
    if isinstance(cm, (int, float)) and isinstance(pm, (int, float)):
        delta = round(cm - pm, 2)
        sign = "↑" if delta > 0 else ("↓" if delta < 0 else "→")
        clause = f"EBITDA margin {trend} ({sign} {abs(delta)} pts) in {period}"

    return f"{clause}. Drivers from alerts and value leakage highlighted."


def _derive_drivers(ctx: Dict[str, Any]) -> List[str]:
    # Prefer analysis.drivers prepared by analyze_margin_node
    analysis = (ctx or {}).get("analysis") or {}
    drivers = analysis.get("drivers")
    if isinstance(drivers, list) and drivers:
        return _safe_str_items(drivers, limit=3)

    # Fallback: synthesize from alerts and value_leakage
    alerts = ((ctx or {}).get("alerts") or {}).get("top_alerts") or []
    leakage_areas = (((ctx or {}).get("mirror") or {}).get("value_leakage") or {}).get("areas") or []
    synthesized: List[str] = []
    synthesized.extend(_safe_str_items(alerts, limit=2))
    # Prefix leakage areas to distinguish from alerts
    for area in _safe_str_items(leakage_areas, limit=2):
        synthesized.append(f"Leakage: {area}")
        if len(synthesized) >= 3:
            break
    return synthesized[:3]


def _derive_next_actions(ctx: Dict[str, Any]) -> List[str]:
    # Use mitigation strategies from value leakage as concrete, non-hallucinated actions
    vl = (((ctx or {}).get("mirror") or {}).get("value_leakage") or {})
    strategies = vl.get("mitigation_strategies") or []
    actions = _safe_str_items(strategies, limit=3)
    # Provide minimal deterministic fallbacks if nothing available
    if not actions:
        actions = [
            "Review EBITDA margin KPI in Digital Mirror to validate time windows",
            "Drill into top alerts to confirm root-cause categories",
        ]
    return actions


def compose_margin_driver(ctx: Dict[str, Any], sources: List[SourceRef]) -> DriverAnalysisModel:
    """
    Compose a concise margin driver analysis leveraging deterministic context assembled by analyze_margin_node.
    - Summary: movement clause for EBITDA margin
    - Drivers: up to 3 items from alerts and value leakage
    - Next actions: up to 3 items, derived from mitigation strategies when available
    Graceful degradation when data is missing; never hallucinate numbers.
    """
    safe_ctx = ctx or {}

    summary = _derive_summary(safe_ctx)
    drivers = _derive_drivers(safe_ctx)
    next_actions = _derive_next_actions(safe_ctx)

    lines: List[str] = []
    if summary:
        lines.append(summary)
    if drivers:
        lines.append(f"Top drivers: {'; '.join(drivers)}.")
    else:
        lines.append("Top drivers: not enough signal from alerts/value leakage.")
    if next_actions:
        lines.append(f"Next actions: {'; '.join(next_actions)}.")

    content = " ".join(lines).strip()

    # UI Action (deep link)
    ui = (safe_ctx.get("uiContext") or {})
    links = DeepLinkBuilder(ui_context=ui)
    actions = [Action(label="Open Digital Mirror (EBITDA margin)", href=links.digital_mirror())]

    return DriverAnalysisModel(content=content, actions=actions)
