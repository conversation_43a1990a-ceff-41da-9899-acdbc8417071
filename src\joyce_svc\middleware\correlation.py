from __future__ import annotations

import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from joyce_svc.logging import set_correlation_id


CORRELATION_HEADER = "X-Correlation-Id"


class CorrelationIdMiddleware(BaseHTTPMiddleware):
    """
    Ensures every request has a correlation ID:
    - Accept X-Correlation-Id header if present; otherwise generate UUID v4.
    - Exposes it via request.state.correlation_id.
    - Sets it in a contextvar for structured logging.
    - Echoes it back in response header X-Correlation-Id.
    """

    async def dispatch(self, request: Request, call_next: Callable):
        # Accept header or generate UUID v4
        header_val = request.headers.get(CORRELATION_HEADER)
        try:
            cid = str(uuid.UUID(header_val)) if header_val else str(uuid.uuid4())
        except ValueError:
            # Malformed incoming value; replace with a valid v4
            cid = str(uuid.uuid4())

        # Attach to request.state and logging context
        request.state.correlation_id = cid
        set_correlation_id(cid)

        try:
            response: Response = await call_next(request)
        finally:
            # Clear correlation ID to avoid leaking across tasks
            set_correlation_id(None)

        response.headers[CORRELATION_HEADER] = cid
        return response
