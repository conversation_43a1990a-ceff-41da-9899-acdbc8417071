"""Unit tests for middleware components."""

import uuid
from unittest.mock import Mock, <PERSON><PERSON><PERSON>
import pytest
from starlette.requests import Request
from starlette.responses import Response

from joyce_svc.middleware.correlation import CorrelationIdMiddleware, CORRELATION_HEADER
from joyce_svc.middleware.rate_limit import RateLimitMiddleware
from joyce_svc.adapters.rate_limit.inproc import InProcRateLimiter
from joyce_svc import di


@pytest.mark.asyncio
async def test_correlation_id_middleware_generates_uuid():
    """CorrelationIdMiddleware should generate UUID when missing."""
    # Mock request without correlation ID header
    request = Mock(spec=Request)
    request.headers = {}
    request.state = Mock()
    
    # Mock call_next
    async def call_next(req):
        # Verify correlation_id was set
        assert hasattr(req.state, "correlation_id")
        assert req.state.correlation_id is not None
        # Validate it's a valid UUID
        uuid.UUID(req.state.correlation_id)  # Will raise if invalid
        return Response("OK")
    
    middleware = CorrelationIdMiddleware(app=None)
    response = await middleware.dispatch(request, call_next)
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_correlation_id_middleware_uses_existing():
    """CorrelationIdMiddleware should use existing X-Correlation-Id."""
    existing_id = "11111111-1111-4111-8111-111111111111"
    
    request = Mock(spec=Request)
    request.headers = {CORRELATION_HEADER: existing_id}
    request.state = Mock()
    
    async def call_next(req):
        assert req.state.correlation_id == existing_id
        return Response("OK")
    
    middleware = CorrelationIdMiddleware(app=None)
    response = await middleware.dispatch(request, call_next)
    assert response.status_code == 200


@pytest.mark.asyncio
async def test_correlation_id_middleware_adds_to_response():
    """CorrelationIdMiddleware should add X-Correlation-Id to response."""
    correlation_id = "22222222-2222-4222-8222-222222222222"
    
    request = Mock(spec=Request)
    request.headers = {CORRELATION_HEADER: correlation_id}
    request.state = Mock()
    
    async def call_next(req):
        return Response("OK")
    
    middleware = CorrelationIdMiddleware(app=None)
    response = await middleware.dispatch(request, call_next)
    
    # Check response headers
    assert response.headers.get("x-correlation-id") == correlation_id


@pytest.mark.asyncio
async def test_rate_limit_middleware_per_ip_tracking(monkeypatch: pytest.MonkeyPatch):
    """RateLimitMiddleware should track requests per IP."""
    limiter = InProcRateLimiter(limit_per_min=2)
    
    # Mock request from IP
    request = Mock(spec=Request)
    request.client = Mock(host="***********")
    request.state = Mock()
    request.headers = {}
    request.url = Mock(path="/api/test")
    
    async def call_next(req):
        return Response("OK")
    
    middleware = RateLimitMiddleware(app=None)
    # Patch middleware-level getter to return our limiter
    monkeypatch.setattr("joyce_svc.middleware.rate_limit.get_rate_limiter", lambda: limiter)
    
    # First request - should pass
    response1 = await middleware.dispatch(request, call_next)
    assert response1.status_code == 200
    
    # Second request - should pass (at limit)
    response2 = await middleware.dispatch(request, call_next)
    assert response2.status_code == 200
    
    # Third request - should be rate limited
    response3 = await middleware.dispatch(request, call_next)
    assert response3.status_code == 429
    assert "Too Many Requests" in response3.body.decode()


@pytest.mark.asyncio
async def test_rate_limit_middleware_different_ips(monkeypatch: pytest.MonkeyPatch):
    """RateLimitMiddleware should track different IPs separately."""
    limiter = InProcRateLimiter(limit_per_min=1)
    
    # Request from first IP
    request1 = Mock(spec=Request)
    request1.client = Mock(host="***********")
    request1.state = Mock()
    request1.headers = {}
    request1.url = Mock(path="/api/test")
    
    # Request from second IP
    request2 = Mock(spec=Request)
    request2.client = Mock(host="***********")
    request2.state = Mock()
    request2.headers = {}
    request2.url = Mock(path="/api/test")
    
    async def call_next(req):
        return Response("OK")
    
    middleware = RateLimitMiddleware(app=None)
    monkeypatch.setattr("joyce_svc.middleware.rate_limit.get_rate_limiter", lambda: limiter)
    
    # Both IPs should get their first request through
    response1 = await middleware.dispatch(request1, call_next)
    assert response1.status_code == 200
    
    response2 = await middleware.dispatch(request2, call_next)
    assert response2.status_code == 200
    
    # Second request from first IP should be limited
    response3 = await middleware.dispatch(request1, call_next)
    assert response3.status_code == 429


def test_auth_middleware_jwt_parsing_conceptual():
    """Conceptual test for JWT parsing errors."""
    # This is a conceptual test showing expected behavior
    # Actual implementation would need proper JWT mocking
    
    class MockAuthMiddleware:
        def parse_jwt(self, token: str):
            if not token:
                raise ValueError("No token provided")
            if token == "invalid.jwt.token":
                raise ValueError("Invalid JWT format")
            if token == "expired.jwt.token":
                raise ValueError("Token expired")
            
            # Valid token
            return {
                "sub": "user123",
                "aud": "joyce-audience",
                "iss": "https://issuer.example.com",
                "tenant": "CVS",
                "role": "analyst",
            }
        
        def handle_auth_error(self, error: Exception):
            return {
                "error": str(error),
                "status": 401,
            }
    
    middleware = MockAuthMiddleware()
    
    # Test various JWT errors
    with pytest.raises(ValueError) as exc:
        middleware.parse_jwt("")
    assert "No token" in str(exc.value)
    
    with pytest.raises(ValueError) as exc:
        middleware.parse_jwt("invalid.jwt.token")
    assert "Invalid JWT" in str(exc.value)
    
    with pytest.raises(ValueError) as exc:
        middleware.parse_jwt("expired.jwt.token")
    assert "expired" in str(exc.value)
    
    # Valid token
    claims = middleware.parse_jwt("valid.jwt.token")
    assert claims["sub"] == "user123"
    assert claims["tenant"] == "CVS"
