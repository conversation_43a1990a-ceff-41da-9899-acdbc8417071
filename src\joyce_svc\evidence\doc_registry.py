from __future__ import annotations

"""
Stable document IDs used for evidence/source references.
These IDs should remain stable across environments to support deduplication and linking.
"""


# Canonical docIds (moved from tools_registry)
DOC_MIRROR_TRENDS = "mirror:kpiTrends"
DOC_SEI_EXEC_SUM = "sei:executive-summary"
DOC_ALERTS = "alerts:summary"


def title_for_doc(doc_id: str) -> str:
    """Human-friendly default titles for known docIds."""
    return {
        DOC_MIRROR_TRENDS: "KPI Trends",
        DOC_SEI_EXEC_SUM: "Executive Summary",
        DOC_ALERTS: "Alerts Summary",
    }.get(doc_id, doc_id)
