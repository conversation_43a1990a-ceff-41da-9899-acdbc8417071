from joyce_svc.agent.persona import render_consultant_voice
from joyce_svc.models.schemas import SourceRef
from joyce_svc.config import settings


def test_consultant_voice_wraps_draft_with_sections_when_enabled_by_default():
    prev = settings.AGENT_LLM_RESPONDER_ENABLED
    settings.AGENT_LLM_RESPONDER_ENABLED = False
    try:
        # Defaults from config: persona enabled, LLM disabled
        draft = (
            "EBITDA margin up (↑ 0.3 pts). Drivers from alerts and value leakage highlighted. "
            "Top drivers: Margin compression in Region A; Leakage: Procurement inefficiencies. "
            "Next actions: Review EBITDA margin KPI; Drill into top alerts."
        )
        ctx = {"uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin"}}
        sources = []  # No citations -> should disclaim

        out = render_consultant_voice(draft=draft, intent="margin_driver", ctx=ctx, sources=sources)
    finally:
        settings.AGENT_LLM_RESPONDER_ENABLED = prev

    # Must contain core sections and disclaimers
    assert "Acknowledgment:" in out
    assert "Validation:" in out
    assert "Key insights:" in out
    assert "Evidence:" in out
    assert "No sources available" in out  # explicit uncertainty/citation disclaimer
    assert "Risks:" in out
    assert "Next steps:" in out

    # Original factual draft content should be preserved in body
    assert "Top drivers:" in out
    assert "Next actions:" in out
    assert "EBITDA margin" in out


def test_consultant_voice_includes_real_citations_when_present():
    prev = settings.AGENT_LLM_RESPONDER_ENABLED
    settings.AGENT_LLM_RESPONDER_ENABLED = False
    try:
        draft = "Summary line. Top drivers: A; B. Next actions: Do X; Do Y."
        ctx = {"uiContext": {"module": "mirror"}}
        sources = [
            SourceRef(kind="internal", docId="mirror:executive-dashboard", title="Executive Summary Dashboard"),
            SourceRef(kind="internal", docId="alerts:summary", title="Alerts Summary"),
        ]

        out = render_consultant_voice(draft=draft, intent="investor_snapshot", ctx=ctx, sources=sources)
    finally:
        settings.AGENT_LLM_RESPONDER_ENABLED = prev

    # Evidence should list docIds (with titles if available)
    assert "mirror:executive-dashboard" in out
    assert "alerts:summary" in out
    assert "Executive Summary Dashboard" in out


def test_consultant_voice_disabled_returns_draft(monkeypatch):
    monkeypatch.setattr(settings, "AGENT_LLM_RESPONDER_ENABLED", False)
    draft = "Plain draft content only."
    ctx = {}
    sources = []

    # Temporarily disable persona and ensure passthrough
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_ENABLED", False)
    out = render_consultant_voice(draft=draft, intent=None, ctx=ctx, sources=sources)
    assert out == draft

    # Restore default for any subsequent tests
    monkeypatch.setattr(settings, "CONSULTANT_VOICE_ENABLED", True)
