from __future__ import annotations

from typing import Dict, Optional


class DeepLinkBuilder:
    """
    Basic deep-link builder that derives hrefs from UI context when available,
    and falls back to existing static paths to preserve current behavior.
    """

    def __init__(self, ui_context: Optional[Dict] = None) -> None:
        self.ui = ui_context or {}

    def _kpi_from_entity(self) -> Optional[str]:
        entity = (self.ui or {}).get("entityId") or ""
        if isinstance(entity, str) and entity.lower().startswith("kpi:"):
            return entity.split(":", 1)[1]
        return None

    def executive_overview(self) -> str:
        # MVP behavior: static path
        return "/sei-report/executive-summary"

    def digital_mirror(self) -> str:
        # Try to derive KPI from uiContext; fallback to ebitda-margin to preserve behavior
        kpi = self._kpi_from_entity() or "ebitda-margin"
        return f"/digital-mirror?kpi={kpi}"

    def execution_roadmap(self) -> str:
        # MVP behavior: static path
        return "/execution/roadmap"

    def recommendations_roadmap(self) -> str:
        # MVP behavior: static path for recommendations
        return "/sei-report/recommendations-roadmap"
