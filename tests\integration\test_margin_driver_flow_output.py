"""Integration tests focused on margin driver flow output content and actions."""

from joyce_svc.agent.graph import get_graph_app
from joyce_svc import di
from joyce_svc.evidence.doc_registry import DOC_MIRROR_TRENDS, DOC_ALERTS


class _FakeTools:
    """Deterministic tools for margin driver flow tests."""
    def fetch_kpi_trends(self, *, company_symbol: str, kpi_name: str):
        from joyce_svc.ports.tools import KPITrendModel
        return KPITrendModel(
            kpi=kpi_name,
            points=[
                {"date": "2025-06", "value": 41.2},
                {"date": "2025-07", "value": 41.8},
                {"date": "2025-08", "value": 42.1},
            ],
        )

    def fetch_executive_summary(self, *, company_symbol: str):
        from joyce_svc.ports.tools import ExecutiveSummaryModel
        return ExecutiveSummaryModel(
            highlights=["Q3 revenue up 5%"],
            risks=["Supply chain pressures"],
            cta="Review detailed metrics",
        )

    def fetch_alerts_summary(self, *, company_symbol: str):
        from joyce_svc.ports.tools import AlertsSummaryModel
        return AlertsSummaryModel(
            categories=["margin", "operations"],
            top_alerts=["Margin compression in Region A", "Inventory buildup"],
        )

    def fetch_ebitda_margin(self, *, company_symbol: str):
        from joyce_svc.ports.tools import EBITDAMarginModel
        return EBITDAMarginModel(
            current_margin=42.1, previous_margin=41.8, trend="up", period="Q3 2025"
        )

    def fetch_value_leakage(self, *, company_symbol: str):
        from joyce_svc.ports.tools import ValueLeakageModel
        return ValueLeakageModel(
            areas=["Procurement inefficiencies"],
            estimated_impact="1.5% margin opportunity",
            mitigation_strategies=["Renegotiate contracts", "Tighten vendor funding controls"],
            priority="high",
        )


def _fresh_graph():
    if hasattr(get_graph_app, "cache_clear"):
        get_graph_app.cache_clear()
    return get_graph_app()


def test_margin_driver_flow_produces_drivers_actions_and_deeplink(monkeypatch):
    monkeypatch.setattr(di, "get_tools", lambda: _FakeTools())
    graph = _fresh_graph()

    initial_state = {
        "query": "What's driving the gross margin change?",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": "test-thread-200",
            "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin"},
            "user": {"id": "u200", "role": "analyst"},
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": [],
    }

    final_state = graph.invoke(initial_state)

    # Intent and context
    assert final_state.intent == "margin_driver"
    ctx = final_state.context
    assert "mirror" in ctx and "ebitda_margin" in ctx["mirror"] and "value_leakage" in ctx["mirror"]
    assert "alerts" in ctx

    # Response content should include summary, drivers, and next actions
    response = final_state.response
    assert isinstance(response, str) and len(response) > 20
    assert "EBITDA margin" in response
    assert "Top drivers:" in response
    assert "Next actions:" in response

    # Deep link action
    assert len(final_state.actions) >= 1
    first_action = final_state.actions[0]
    assert first_action.label == "Open Digital Mirror (EBITDA margin)"
    assert first_action.href == "/digital-mirror?kpi=ebitda-margin"

    # Evidence contains expected docIds
    doc_ids = {e.docId for e in final_state.evidence}
    assert DOC_MIRROR_TRENDS in doc_ids or DOC_ALERTS in doc_ids
