from __future__ import annotations

from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, ConfigDict, Field

from joyce_svc.models.schemas import Action, SourceRef


# Allowed intents for deterministic routing
Intent = Literal["investor_snapshot", "margin_driver", "execution_proposal"]


class JoyceState(BaseModel):
    """
    Typed state passed between LangGraph nodes for Joyce.

    Fields:
    - query: original user message (AdvisorV1ChatRequest.message)
    - intent: one of the allowed intents; None until classified
    - context: arbitrary context (tenantId, threadId, uiContext, user role, etc.)
    - tools_to_call: registry hints or chosen tools for subsequent nodes
    - evidence: accumulated SourceRef entries used for citations
    - response: composed final response text (filled by structure_response)
    - actions: UI actions to present in the final frame
    """

    model_config = ConfigDict(extra="forbid")

    query: str
    intent: Optional[Intent] = None
    context: Dict[str, Any] = Field(default_factory=dict)
    tools_to_call: List[str] = Field(default_factory=list)
    evidence: List[SourceRef] = Field(default_factory=list)
    response: str = Field(default="", description="Composed final response")
    actions: List[Action] = Field(default_factory=list)
