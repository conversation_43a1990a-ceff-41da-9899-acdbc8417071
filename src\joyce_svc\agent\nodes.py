from __future__ import annotations

import re
from typing import Any, Dict, List

from joyce_svc.agent.types import JoyceState, Intent
from joyce_svc.config import settings
from joyce_svc.logging import get_logger
from joyce_svc.di import get_tools, get_intent_classifier, get_memory
from joyce_svc.adapters.evidence.basic import EvidenceRecorder
from joyce_svc.evidence.doc_registry import DOC_MIRROR_TRENDS, DOC_SEI_EXEC_SUM, DOC_ALERTS
from joyce_svc.agent.flows.investor_snapshot.composer import compose_investor_snapshot
from joyce_svc.agent.flows.margin_driver.composer import compose_margin_driver
from joyce_svc.agent.flows.execution_proposal.composer import compose_execution_proposal
from joyce_svc.agent.persona import render_consultant_voice
from joyce_svc.agent.planner import plan_with_llm
from joyce_svc.errors import ToolError


# -------- Helpers --------

logger = get_logger(__name__)


def _append_evidence(existing: List, new_items: List):
    """Append SourceRef items avoiding duplicate docIds."""
    seen = {e.docId for e in existing}
    for item in new_items:
        if item.docId not in seen:
            existing.append(item)
            seen.add(item.docId)


def _safe_context(state: JoyceState) -> Dict[str, Any]:
    ctx = dict(state.context or {})
    if "uiContext" not in ctx:
        ctx["uiContext"] = {}
    return ctx


_DEFAULT_TOOLS_BY_INTENT: Dict[str, List[str]] = {
    "investor_snapshot": ["fetch_mirror_data", "fetch_sei_data", "fetch_alerts_data"],
    "margin_driver": ["fetch_mirror_data", "fetch_alerts_data", "analyze_margin"],
    "execution_proposal": ["fetch_sei_data"],
}

_KNOWN_TOOL_NODES = {
    "fetch_mirror_data",
    "fetch_sei_data",
    "fetch_alerts_data",
    "analyze_margin",
}


def _should_skip_tool(state: JoyceState, tool_name: str) -> bool:
    hints = getattr(state, "tools_to_call", None) or []
    if not hints:
        return False
    return tool_name not in hints


# -------- Node implementations --------


def _llm_intent_fallback(text: str):
    """
    Delegates to DI-provided intent classifier (rule-first with optional LLM).
    Returns one of: investor_snapshot | margin_driver | execution_proposal | None
    """
    try:
        clf = get_intent_classifier()
        res = clf.classify(text)
        intent = res.get("intent")
        if intent:
            logger.debug("Intent classifier selected", extra={"intent": intent})
            return intent
    except Exception:
        logger.debug("Intent classification failed; falling back to clarify", exc_info=True)
    return None


def classify_intent_node(state: JoyceState) -> Dict[str, Any]:
    """
    Deterministic rule-based intent classifier.
    Keeps temperature=0 behavior by avoiding LLM calls.
    """
    ctx = _safe_context(state)
    text = (state.query or "").lower()

    # Attempt LLM-driven planning first when enabled.
    plan = plan_with_llm(state.query, ctx=ctx)
    if plan and plan.intent in {"investor_snapshot", "margin_driver", "execution_proposal"}:
        planned_tools = [tool for tool in plan.tools if tool in _KNOWN_TOOL_NODES]
        tools = planned_tools if planned_tools else list(_DEFAULT_TOOLS_BY_INTENT.get(plan.intent, []))
        ctx.setdefault("planning", {})
        ctx["planning"].update(
            {
                "reasoning": plan.reasoning,
                "confidence": plan.confidence,
            }
        )
        if plan.clarifying_question:
            ctx["planning"]["clarifying_question"] = plan.clarifying_question
        return {
            "intent": plan.intent,
            "tools_to_call": tools,
            "context": ctx,
        }
    elif plan and plan.clarifying_question:
        ctx.setdefault("planning", {})
        ctx["planning"]["clarifying_question"] = plan.clarifying_question

    def has(*words: str) -> bool:
        return all(w in text for w in words)

    ctx.setdefault("planning", {})

    intent: Intent
    # Investor brief signals
    if has("investor") or has("brief") or has("5-bullet") or has("5 bullet"):
        ctx["planning"].setdefault("reasoning", "Rule-based match for investor brief keywords")
        tools = list(_DEFAULT_TOOLS_BY_INTENT.get("investor_snapshot", []))
        return {"intent": "investor_snapshot", "tools_to_call": tools, "context": ctx}
    # Margin driver analysis
    elif (has("margin") and has("driver")) or "what's driving" in text or "whats driving" in text or "what is driving" in text:
        ctx["planning"].setdefault("reasoning", "Rule-based match for margin driver phrasing")
        tools = list(_DEFAULT_TOOLS_BY_INTENT.get("margin_driver", []))
        return {"intent": "margin_driver", "tools_to_call": tools, "context": ctx}
    # Execution proposal
    elif "execution" in text or "proposal" in text or ("owners" in text and "timeline" in text):
        ctx["planning"].setdefault("reasoning", "Rule-based match for execution proposal keywords")
        tools = list(_DEFAULT_TOOLS_BY_INTENT.get("execution_proposal", []))
        return {"intent": "execution_proposal", "tools_to_call": tools, "context": ctx}

    # Try optional deterministic classifier/LLM ensemble
    llm_intent = _llm_intent_fallback(text)
    if llm_intent:
        ctx["planning"].setdefault("reasoning", "LLM ensemble intent fallback")
        tools = list(_DEFAULT_TOOLS_BY_INTENT.get(llm_intent, []))
        return {"intent": llm_intent, "tools_to_call": tools, "context": ctx}

    return {"intent": None, "context": ctx}


def fetch_mirror_data_node(state: JoyceState) -> Dict[str, Any]:
    """
    Fetch KPI trends from Mirror (file-backed via ToolsPort).
    Also enrich with EBITDA margin summary for investor snapshot formatting.
    """
    ctx = _safe_context(state)
    if _should_skip_tool(state, "fetch_mirror_data"):
        return {"context": ctx, "evidence": list(state.evidence)}
    # Derive company symbol and KPI if available; default to CVS / EBITDA margin for MVP
    company_symbol = ctx.get("tenantId", "CVS")
    # Try infer KPI from route or entityId
    ui_entity = (ctx.get("uiContext") or {}).get("entityId") or ""
    m = re.search(r"kpi:([a-z0-9\-:_]+)", ui_entity, re.IGNORECASE)
    kpi_name = m.group(1) if m else "ebitda-margin"

    tools = get_tools()
    rec = EvidenceRecorder(seed=list(state.evidence))

    # KPI trend series
    try:
        trends_model = tools.fetch_kpi_trends(company_symbol=company_symbol, kpi_name=kpi_name)
    except Exception as e:
        raise ToolError(f"fetch_kpi_trends failed: {e}") from e

    # Initialize mirror context
    ctx["mirror"] = {"kpi": kpi_name, "trends": trends_model.model_dump()}
    rec.record(doc_id=DOC_MIRROR_TRENDS)

    # EBITDA margin summary (used as proxy for gross margin in MVP)
    try:
        margin_model = tools.fetch_ebitda_margin(company_symbol=company_symbol)
        ctx["mirror"]["ebitda_margin"] = margin_model.model_dump()
        # Evidence remains the Mirror dashboard doc
        rec.record(doc_id=DOC_MIRROR_TRENDS)
    except Exception as e:
        raise ToolError(f"fetch_ebitda_margin failed: {e}") from e

    return {"context": ctx, "evidence": rec.sources()}


def fetch_sei_data_node(state: JoyceState) -> Dict[str, Any]:
    """
    Fetch SEI executive summary (file-backed via ToolsPort).
    """
    ctx = _safe_context(state)
    if _should_skip_tool(state, "fetch_sei_data"):
        return {"context": ctx, "evidence": list(state.evidence)}
    company_symbol = ctx.get("tenantId", "CVS")
    tools = get_tools()
    try:
        exec_sum_model = tools.fetch_executive_summary(company_symbol=company_symbol)
    except Exception as e:
        raise ToolError(f"fetch_executive_summary failed: {e}") from e
    ctx["sei"] = exec_sum_model.model_dump()

    rec = EvidenceRecorder(seed=list(state.evidence))
    rec.record(doc_id=DOC_SEI_EXEC_SUM)
    return {"context": ctx, "evidence": rec.sources()}


def fetch_alerts_data_node(state: JoyceState) -> Dict[str, Any]:
    """
    Fetch Alerts summary (file-backed via ToolsPort) for top investor-relevant alerts.
    """
    ctx = _safe_context(state)
    if _should_skip_tool(state, "fetch_alerts_data"):
        return {"context": ctx, "evidence": list(state.evidence)}
    company_symbol = ctx.get("tenantId", "CVS")
    tools = get_tools()
    try:
        alerts_model = tools.fetch_alerts_summary(company_symbol=company_symbol)
    except Exception as e:
        raise ToolError(f"fetch_alerts_summary failed: {e}") from e
    ctx["alerts"] = alerts_model.model_dump()

    rec = EvidenceRecorder(seed=list(state.evidence))
    rec.record(doc_id=DOC_ALERTS)
    return {"context": ctx, "evidence": rec.sources()}


def analyze_margin_node(state: JoyceState) -> Dict[str, Any]:
    """
    Analyze margin using KPI trends, EBITDA margin, value leakage, and alerts.
    """
    ctx = _safe_context(state)
    if _should_skip_tool(state, "analyze_margin"):
        return {"context": ctx, "evidence": list(state.evidence)}
    company_symbol = ctx.get("tenantId", "CVS")
    tools = get_tools()
    rec = EvidenceRecorder(seed=list(state.evidence))

    # Ensure trends present
    if "mirror" not in ctx:
        # default fetch for ebitda-margin
        try:
            trends_model = tools.fetch_kpi_trends(company_symbol=company_symbol, kpi_name="ebitda-margin")
        except Exception as e:
            raise ToolError(f"fetch_kpi_trends failed: {e}") from e
        ctx["mirror"] = {"kpi": "ebitda-margin", "trends": trends_model.model_dump()}
        rec.record(doc_id=DOC_MIRROR_TRENDS)

    # Enrich with EBITDA margin summary
    try:
        margin_model = tools.fetch_ebitda_margin(company_symbol=company_symbol)
        # Attach under mirror key
        ctx.setdefault("mirror", {})
        ctx["mirror"]["ebitda_margin"] = margin_model.model_dump()
        # Evidence still points to mirror dashboard doc
        rec.record(doc_id=DOC_MIRROR_TRENDS)
    except Exception as e:
        raise ToolError(f"fetch_ebitda_margin failed: {e}") from e

    # Enrich with value leakage analysis
    try:
        vl_model = tools.fetch_value_leakage(company_symbol=company_symbol)
        ctx.setdefault("mirror", {})
        ctx["mirror"]["value_leakage"] = vl_model.model_dump()
        rec.record(doc_id=DOC_MIRROR_TRENDS)
    except Exception as e:
        raise ToolError(f"fetch_value_leakage failed: {e}") from e

    # Alerts summary (for top drivers)
    try:
        alerts_model = tools.fetch_alerts_summary(company_symbol=company_symbol)
    except Exception as e:
        raise ToolError(f"fetch_alerts_summary failed: {e}") from e
    ctx["alerts"] = alerts_model.model_dump()
    rec.record(doc_id=DOC_ALERTS)

    # Summarize analysis note for structure_response to consume
    top_alerts = (ctx.get("alerts") or {}).get("top_alerts", [])[:2]
    leakage_areas = (ctx.get("mirror") or {}).get("value_leakage", {}).get("areas", [])[:2]
    drivers: List[str] = list(top_alerts)
    # Prefix leakage areas to distinguish from alerts
    for area in leakage_areas:
        if isinstance(area, str):
            drivers.append(f"Leakage: {area}")

    margin = (ctx.get("mirror") or {}).get("ebitda_margin", {}) or {}
    trend = margin.get("trend", "stable")
    cm = margin.get("current_margin")
    pm = margin.get("previous_margin")
    margin_clause = f"EBITDA margin {trend}"
    if isinstance(cm, (int, float)) and isinstance(pm, (int, float)):
        delta = round(cm - pm, 2)
        sign = "↑" if delta > 0 else ("↓" if delta < 0 else "→")
        margin_clause = f"EBITDA margin {trend} ({sign} {abs(delta)} pts)"

    ctx["analysis"] = {
        "summary": f"{margin_clause}. Drivers from alerts and value leakage highlighted.",
        "drivers": drivers,
    }
    return {"context": ctx, "evidence": rec.sources()}


def clarify_question_node(state: JoyceState) -> Dict[str, Any]:
    """
    Ask a minimal clarifying question when intent is unknown/low-confidence.
    """
    response = (
        "To help me route correctly, are you asking for an investor snapshot, "
        "margin drivers, or an execution proposal?"
    )
    return {"response": response}


def load_memory_node(state: JoyceState) -> Dict[str, Any]:
    """
    Optional memory load. No-ops unless settings.MEMORY_ENABLED is True.
    """
    if not settings.MEMORY_ENABLED:
        return {}
    ctx = _safe_context(state)
    thread_id = ctx.get("threadId")
    if not thread_id:
        return {"context": ctx}
    try:
        store = get_memory()
        snapshot = store.load(thread_id=thread_id)
        # Attach minimal memory context for downstream prompt composition (future use)
        ctx["memory"] = {
            "turns": [t.model_dump() for t in snapshot.turns],
            "user_role": snapshot.user_role,
        }
    except Exception:
        # Memory is best-effort; never break flow
        pass
    return {"context": ctx}


def save_memory_node(state: JoyceState) -> Dict[str, Any]:
    """
    Optional memory save. No-ops unless settings.MEMORY_ENABLED is True.
    Persists the last user query and assistant response for the thread.
    """
    if not settings.MEMORY_ENABLED:
        return {}
    try:
        store = get_memory()
        ctx = _safe_context(state)
        thread_id = ctx.get("threadId")
        if not thread_id:
            return {}
        ui_ctx = ctx.get("uiContext") or {}
        user_role = (ctx.get("user") or {}).get("role")
        # Save user and assistant turns
        from joyce_svc.ports.memory import MemoryTurn  # local import to avoid circular at module import time
        if state.query:
            store.save(thread_id=thread_id, turn=MemoryTurn(role="user", content=state.query), ui_context=ui_ctx, user_role=user_role)
        if state.response:
            store.save(thread_id=thread_id, turn=MemoryTurn(role="assistant", content=state.response), ui_context=ui_ctx, user_role=user_role)
    except Exception:
        # Best-effort; do not break flow
        pass
    return {}


def structure_response_node(state: JoyceState) -> Dict[str, Any]:
    """
    Compose final consultant-style response text and actions, using per-flow composers.
    Behavior preserved; only refactored to delegate to flow-specific composers.
    """
    ctx = _safe_context(state)
    intent = state.intent
    sources = list(state.evidence)
    actions = list(state.actions)

    if intent == "investor_snapshot":
        out = compose_investor_snapshot(ctx, sources)
        final_text = render_consultant_voice(
            draft=out.content,
            intent=intent,
            ctx=ctx,
            sources=sources,
            actions=out.actions,
        )
        return {"response": final_text, "actions": out.actions}

    if intent == "margin_driver":
        out = compose_margin_driver(ctx, sources)
        final_text = render_consultant_voice(
            draft=out.content,
            intent=intent,
            ctx=ctx,
            sources=sources,
            actions=out.actions,
        )
        return {"response": final_text, "actions": out.actions}

    if intent == "execution_proposal":
        out = compose_execution_proposal(ctx, sources)
        final_text = render_consultant_voice(
            draft=out.content,
            intent=intent,
            ctx=ctx,
            sources=sources,
            actions=out.actions,
        )
        return {"response": final_text, "actions": out.actions}

    # Fallback: keep clarify response and any existing actions
    response = state.response or "Could you clarify if you want a brief, driver analysis, or an execution plan?"
    return {"response": response, "actions": actions}
