from __future__ import annotations

from functools import lru_cache
from typing import Dict, Optional

from langgraph.graph import END, START, StateGraph

from joyce_svc.config import settings
from joyce_svc.agent.types import Joyce<PERSON><PERSON>, Intent
from joyce_svc.agent.nodes import (
    classify_intent_node,
    fetch_mirror_data_node,
    fetch_sei_data_node,
    fetch_alerts_data_node,
    analyze_margin_node,
    clarify_question_node,
    structure_response_node,
    load_memory_node,
    save_memory_node,
)
from joyce_svc.agent.flows_registry import first_step_for, linear_edges


def _route_from_intent(state: JoyceState) -> str:
    """
    Conditional edge router based on classified intent.
    Returns the label consumed by add_conditional_edges mapping.
    """
    intent: Optional[Intent] = state.intent  # type: ignore[attr-defined]
    if intent == "investor_snapshot":
        return "investor_snapshot"
    if intent == "margin_driver":
        return "margin_driver"
    if intent == "execution_proposal":
        return "execution_proposal"
    return "fallback"


@lru_cache(maxsize=1)
def get_graph_app():
    """
    Build and cache the compiled LangGraph application for <PERSON>.
    Returns a wrapper whose invoke(...) returns a JoyceState instance.
    """
    graph = StateGraph(JoyceState)

    # Nodes
    graph.add_node("classify_intent", classify_intent_node)
    graph.add_node("fetch_mirror_data", fetch_mirror_data_node)
    graph.add_node("fetch_sei_data", fetch_sei_data_node)
    graph.add_node("fetch_alerts_data", fetch_alerts_data_node)
    graph.add_node("analyze_margin", analyze_margin_node)
    graph.add_node("clarify_question", clarify_question_node)
    graph.add_node("structure_response", structure_response_node)

    # Entry (optionally load memory before classify)
    if settings.MEMORY_ENABLED:
        graph.add_node("load_memory", load_memory_node)
        graph.add_edge(START, "load_memory")
        graph.add_edge("load_memory", "classify_intent")
    else:
        graph.add_edge(START, "classify_intent")

    # Conditional routing from classifier
    graph.add_conditional_edges(
        "classify_intent",
        _route_from_intent,
        {
            "investor_snapshot": first_step_for("investor_snapshot"),
            "margin_driver": first_step_for("margin_driver"),
            "execution_proposal": first_step_for("execution_proposal"),
            "fallback": first_step_for("fallback"),
        },
    )

    # Linear edges computed from registry
    for a, b in linear_edges():
        graph.add_edge(a, b)

    # Terminal (optionally save memory after compose)
    if settings.MEMORY_ENABLED:
        graph.add_node("save_memory", save_memory_node)
        graph.add_edge("structure_response", "save_memory")
        graph.add_edge("save_memory", END)
    else:
        graph.add_edge("structure_response", END)

    compiled = graph.compile()

    class _GraphWrapper:
        def __init__(self, inner):
            self._inner = inner

        def invoke(self, state, *args, **kwargs):
            result = self._inner.invoke(state, *args, **kwargs)
            if isinstance(result, JoyceState):
                return result
            try:
                return JoyceState(**result)
            except Exception:
                # Preserve prior dict behavior on unexpected shapes
                return result

        def astream(self, *args, **kwargs):
            # Pass-through streaming interface if needed elsewhere
            return self._inner.astream(*args, **kwargs)

    return _GraphWrapper(compiled)
