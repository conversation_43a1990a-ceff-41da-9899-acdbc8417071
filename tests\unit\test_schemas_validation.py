"""Unit tests for advisor_v1 schema validation."""

import pytest
from pydantic import ValidationError

from joyce_svc.models.schemas import (
    ChatRequest,
    UserInfo,
    UIContext,
    AdvisorFrame,
    Meta,
    Action,
    SourceRef,
)


def test_chat_request_rejects_invalid_response_format():
    """ChatRequest must have response_format='advisor_v1'."""
    with pytest.raises(ValidationError) as exc_info:
        ChatRequest(
            tenantId="CVS",
            user=UserInfo(id="u1", role="analyst"),
            threadId="t1",
            uiContext=UIContext(module="mirror"),
            message="test",
            response_format="invalid_format",  # Should be 'advisor_v1'
        )
    assert "advisor_v1" in str(exc_info.value)


def test_chat_request_forbids_extra_fields():
    """ChatRequest should reject extra fields."""
    with pytest.raises(ValidationError) as exc_info:
        ChatRequest(
            tenantId="CVS",
            user=UserInfo(id="u1", role="analyst"),
            threadId="t1",
            uiContext=UIContext(module="mirror"),
            message="test",
            response_format="advisor_v1",
            extra_field="not_allowed",  # Extra field
        )
    assert "extra" in str(exc_info.value).lower()


def test_advisor_frame_final_requires_actions_sources():
    """Final frame must include actions and sources (can be empty)."""
    # Valid final frame with empty arrays
    frame = AdvisorFrame(
        type="final",
        content="Response text",
        actions=[],
        sources=[],
        meta=Meta(correlationId="123"),
    )
    assert frame.type == "final"
    assert frame.actions == []
    assert frame.sources == []

    # Final frame with populated arrays
    frame_with_data = AdvisorFrame(
        type="final",
        content="Response",
        actions=[Action(label="View", href="/path")],
        sources=[SourceRef(kind="internal", docId="doc1")],
        meta=Meta(correlationId="456"),
    )
    assert len(frame_with_data.actions) == 1
    assert len(frame_with_data.sources) == 1


def test_advisor_frame_chunk_requires_content():
    """Chunk frames must have content."""
    # Valid chunk
    chunk = AdvisorFrame(
        type="chunk",
        content="Streaming text...",
        meta=Meta(correlationId="789"),
    )
    assert chunk.content == "Streaming text..."

    # Chunk without content should still validate (content is Optional)
    # but in practice the route enforces it
    chunk_empty = AdvisorFrame(
        type="chunk",
        content=None,
        meta=Meta(correlationId="789"),
    )
    assert chunk_empty.content is None


def test_meta_correlation_id_handling():
    """Meta should handle correlationId presence/absence."""
    # With correlationId
    meta_with = Meta(correlationId="abc-123")
    assert meta_with.correlationId == "abc-123"

    # Without correlationId
    meta_without = Meta()
    assert meta_without.correlationId is None

    # With timing
    meta_timing = Meta(correlationId="def-456", timing={"ttfbMs": 350})
    assert meta_timing.timing.ttfbMs == 350


def test_ui_context_optional_fields():
    """UIContext requires module but entityId/route are optional."""
    # Minimal
    ctx_min = UIContext(module="mirror")
    assert ctx_min.module == "mirror"
    assert ctx_min.entityId is None
    assert ctx_min.route is None

    # Full
    ctx_full = UIContext(
        module="sei",
        entityId="kpi:revenue",
        route="/sei-report?kpi=revenue",
    )
    assert ctx_full.entityId == "kpi:revenue"
    assert ctx_full.route == "/sei-report?kpi=revenue"


def test_source_ref_validation():
    """SourceRef requires kind and docId."""
    # Valid internal
    src = SourceRef(kind="internal", docId="DOC_MIRROR_TRENDS")
    assert src.kind == "internal"
    assert src.docId == "DOC_MIRROR_TRENDS"

    # Valid external with URL
    src_ext = SourceRef(
        kind="external",
        docId="ext_123",
        title="External Report",
        url="https://example.com/report",
    )
    assert src_ext.kind == "external"
    assert src_ext.url == "https://example.com/report"

    # Invalid kind
    with pytest.raises(ValidationError):
        SourceRef(kind="invalid_kind", docId="doc1")
