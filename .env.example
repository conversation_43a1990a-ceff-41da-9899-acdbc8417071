# Local Development Defaults (safe, offline by default)
# Copy to .env for local runs:  cp .env.example .env

# --- LLM Classification (optional fallback) ---
# Model used when JOYCE_LLM_CLASSIFIER=true and rule-based classifier cannot decide.
# You can override this per-environment without code changes.
INTENT_LLM_MODEL=claude-sonnet-4-********

# Feature flag to enable the Anthropic LLM fallback for intent classification.
# Default is true (used to be false) for MVP (deterministic, fast). Set to true to allow fallback.
JOYCE_LLM_CLASSIFIER=true

# Anthropic API key (required only if JOYCE_LLM_CLASSIFIER=true)
# Obtain from your Anthropic account. Example: sk-ant-...
ANTHROPIC_API_KEY=************************************************************************************************************

# --- Auth / Dev toggles (example) ---
# Set to false for local smoke tests without JWT.
# For production/stage, this should remain true and OIDC vars must be configured.
# AUTH_REQUIRED=false

# Local Development Defaults (safe, offline by default)
# Copy to .env for local runs:  cp .env.example .env

# Core
LOG_LEVEL=info
PORT=8080
AUTH_REQUIRED=false

# CORS (adjust as needed)
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000

# Data root (uses repo dev data by default)
DATA_ROOT=${PWD}/dev-data

# Behavior & perf
ENABLE_UI_ACTIONS=true
STREAM_FROM_GRAPH=false
START_CHUNK_DELAY_MS=200
REQUEST_TIMEOUT_S=40
RATE_LIMIT_PER_MIN=60

# Auth (OIDC/JWT) - leave unset for local unauth requests (AUTH_REQUIRED=false)
# OIDC_ISSUER=
# OIDC_AUDIENCE=
# OIDC_JWKS_URL=
# OIDC_JWKS_PATH=

# Memory (MVP in-proc)
MEMORY_ENABLED=false
MEMORY_WINDOW=8
MEMORY_TTL_MIN=30

# LLM intent classifier (OFF by default for local)
JOYCE_LLM_CLASSIFIER=true
INTENT_LLM_MODEL=claude-sonnet-4-********

# Consultant persona (JOYCE-009)
CONSULTANT_VOICE_ENABLED=true
CONSULTANT_VOICE_LLM_ENABLED=true
# If unset, CONSULTANT_VOICE_MODEL defaults to INTENT_LLM_MODEL
# CONSULTANT_VOICE_MODEL=claude-sonnet-4-********
CONSULTANT_VOICE_TIMEOUT_MS=800

# Secrets (leave unset locally unless testing with real providers)
# ANTHROPIC_API_KEY=
# OPENAI_API_KEY=

# Metrics placeholder
METRICS_ENABLED=false
