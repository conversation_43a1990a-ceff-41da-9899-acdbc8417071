from __future__ import annotations

import time
from collections import defaultdict, deque
from typing import Deque, Dict, Optional

from joyce_svc.ports.memory import MemoryPort, MemorySnapshot, MemoryTurn


class InprocMemoryStore(MemoryPort):
    """
    Simple in-process memory store with per-thread buffers and TTL.
    - Not shared across processes.
    - Intended as a scaffold; enabled/used only when MEMORY_ENABLED is True.
    """

    def __init__(self, *, window: int = 8, ttl_min: int = 30) -> None:
        self._window = max(1, int(window))
        self._ttl_sec = max(1, int(ttl_min)) * 60
        self._bufs: Dict[str, Deque[tuple[float, MemoryTurn]]] = defaultdict(deque)
        self._ui_ctx: Dict[str, dict] = {}
        self._user_role: Dict[str, Optional[str]] = {}

    def _prune(self, thread_id: str) -> None:
        now = time.time()
        buf = self._bufs[thread_id]
        # Remove expired
        cutoff = now - self._ttl_sec
        while buf and buf[0][0] < cutoff:
            buf.popleft()
        # Enforce window size
        while len(buf) > self._window:
            buf.popleft()

    def load(self, *, thread_id: str) -> MemorySnapshot:
        self._prune(thread_id)
        turns = [t for _, t in list(self._bufs[thread_id])]
        return MemorySnapshot(
            thread_id=thread_id,
            turns=turns,
            ui_context=self._ui_ctx.get(thread_id, {}) or {},
            user_role=self._user_role.get(thread_id),
        )

    def save(
        self,
        *,
        thread_id: str,
        turn: MemoryTurn,
        ui_context: Optional[dict] = None,
        user_role: Optional[str] = None,
    ) -> None:
        now = time.time()
        self._bufs[thread_id].append((now, turn))
        self._prune(thread_id)
        if ui_context is not None:
            self._ui_ctx[thread_id] = dict(ui_context)
        if user_role is not None:
            self._user_role[thread_id] = user_role

    def trim(self, *, thread_id: str, window: int) -> None:
        self._window = max(1, int(window))
        self._prune(thread_id)
