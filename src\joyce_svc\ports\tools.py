from __future__ import annotations

from typing import Protocol, List, Dict, Any
from pydantic import BaseModel, ConfigDict, Field


# Pydantic models for tool outputs (exported here for convenience)
class KPITrendModel(BaseModel):
    model_config = ConfigDict(extra="forbid")
    kpi: str = Field(..., description="KPI name (e.g., ebitda-margin)")
    points: List[Dict[str, Any]] = Field(
        ..., description='Time series points e.g., [{"date":"2025-08","value":42.1}]'
    )


class ExecutiveSummaryModel(BaseModel):
    model_config = ConfigDict(extra="forbid")
    highlights: List[str]
    risks: List[str]
    cta: str


class AlertsSummaryModel(BaseModel):
    model_config = ConfigDict(extra="forbid")
    categories: List[str]
    top_alerts: List[str]


class EBITDAMarginModel(BaseModel):
    model_config = ConfigDict(extra="forbid")
    current_margin: float
    previous_margin: float | None = None
    trend: str
    period: str


class ValueLeakageModel(BaseModel):
    model_config = ConfigDict(extra="forbid")
    areas: List[str]
    estimated_impact: str | None = None
    mitigation_strategies: List[str]
    priority: str


# Tool service protocol (port)
class ToolsPort(Protocol):
    def fetch_kpi_trends(self, *, company_symbol: str, kpi_name: str) -> KPITrendModel: ...
    def fetch_executive_summary(self, *, company_symbol: str) -> ExecutiveSummaryModel: ...
    def fetch_alerts_summary(self, *, company_symbol: str) -> AlertsSummaryModel: ...
    def fetch_ebitda_margin(self, *, company_symbol: str) -> EBITDAMarginModel: ...
    def fetch_value_leakage(self, *, company_symbol: str) -> ValueLeakageModel: ...
