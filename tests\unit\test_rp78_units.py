import json
from pathlib import Path

import pytest

from joyce_svc.adapters.tools.file_backed import FileBackedToolsAdapter
from joyce_svc.ports.tools import (
    KPITrendModel,
    ExecutiveSummaryModel,
    AlertsSummaryModel,
    EBITDAMarginModel,
    ValueLeakageModel,
)
from joyce_svc import di
from joyce_svc.agent.types import JoyceState
from joyce_svc.agent.nodes import analyze_margin_node


def _write_company_fixtures(base: Path) -> None:
    """Create minimal company fixtures for unit tests under base/companies/cvs/..."""
    era_dir = base / "companies" / "cvs" / "era-report"
    era_dir.mkdir(parents=True, exist_ok=True)
    (base / "companies" / "cvs").mkdir(parents=True, exist_ok=True)

    dashboard = {
        "kpis": {
            "ebitda-margin": {
                "current_value": 42.1,
                "previous_value": 41.8,
                "period": "Q3 2025",
                "unit": "%",
            }
        },
        "financial_metrics": {
            "ebitda_margin": {
                "current_margin": 42.1,
                "previous_margin": 41.8,
                "period": "Q3 2025",
            }
        },
        "value_leakage": {
            "areas": ["Supply Chain Inefficiencies", "Inventory Optimization"],
            "estimated_impact": "2.3% margin improvement potential",
            "mitigation_strategies": ["Optimize supplier contracts"],
            "priority": "high",
        },
    }
    with (era_dir / "executive-summary-dashboard.json").open("w", encoding="utf-8") as f:
        json.dump(dashboard, f)

    exec_summary = {
        "key_highlights": ["Strong Q3 performance"],
        "risks": ["Labor inflation"],
        "recommendations": ["Accelerate digital health investments"],
        "overall_sentiment": "positive",
    }
    with (base / "companies" / "cvs" / "executive-summary.json").open("w", encoding="utf-8") as f:
        json.dump(exec_summary, f)

    alerts = {
        "alerts": {
            "high_priority": ["Supply chain optimization needed"],
            "operations": ["Traffic shift vs. historical"],
        }
    }
    with (base / "companies" / "cvs" / "alerts.json").open("w", encoding="utf-8") as f:
        json.dump(alerts, f)


def test_file_backed_tools_happy_path_and_defaults(tmp_path: Path):
    data_root = tmp_path
    _write_company_fixtures(data_root)

    adapter = FileBackedToolsAdapter(str(data_root))

    # KPI trends
    kpi = adapter.fetch_kpi_trends(company_symbol="CVS", kpi_name="ebitda-margin")
    assert isinstance(kpi, KPITrendModel)
    assert kpi.kpi == "ebitda-margin"
    assert len(kpi.points) == 3
    assert kpi.points[-1]["value"] == 42.1

    # Exec summary
    es = adapter.fetch_executive_summary(company_symbol="CVS")
    assert isinstance(es, ExecutiveSummaryModel)
    assert "Strong Q3 performance" in es.highlights[0]
    assert isinstance(es.cta, str) and es.cta

    # Alerts
    al = adapter.fetch_alerts_summary(company_symbol="CVS")
    assert isinstance(al, AlertsSummaryModel)
    assert "operations" in al.categories
    assert len(al.top_alerts) >= 1

    # EBITDA margin & value leakage
    em = adapter.fetch_ebitda_margin(company_symbol="CVS")
    assert isinstance(em, EBITDAMarginModel)
    assert em.current_margin == 42.1
    assert em.trend in ("up", "down", "stable")

    vl = adapter.fetch_value_leakage(company_symbol="CVS")
    assert isinstance(vl, ValueLeakageModel)
    assert "Supply Chain Inefficiencies" in vl.areas

    # Defaults for unknown company
    unk = adapter.fetch_kpi_trends(company_symbol="UNKNOWN", kpi_name="revenue")
    assert isinstance(unk, KPITrendModel)
    assert unk.points and isinstance(unk.points[0].get("value"), (int, float))


class _FakeTools:
    def fetch_kpi_trends(self, *, company_symbol: str, kpi_name: str) -> KPITrendModel:
        return KPITrendModel(kpi=kpi_name, points=[{"date": "2025-08", "value": 1.0}])

    def fetch_executive_summary(self, *, company_symbol: str) -> ExecutiveSummaryModel:
        return ExecutiveSummaryModel(highlights=["H1"], risks=["R1"], cta="CTA")

    def fetch_alerts_summary(self, *, company_symbol: str) -> AlertsSummaryModel:
        return AlertsSummaryModel(categories=["c1"], top_alerts=["A1", "A2"])

    def fetch_ebitda_margin(self, *, company_symbol: str) -> EBITDAMarginModel:
        return EBITDAMarginModel(current_margin=40.0, previous_margin=39.5, trend="up", period="Q3")

    def fetch_value_leakage(self, *, company_symbol: str) -> ValueLeakageModel:
        return ValueLeakageModel(areas=["Area1"], estimated_impact="1%", mitigation_strategies=["M1"], priority="high")


def test_tools_registry_wrappers(monkeypatch: pytest.MonkeyPatch):
    # Patch DI to return fake tools
    monkeypatch.setattr(di, "get_tools", lambda: _FakeTools())

    from joyce_svc.agent.tools_registry import (
        fetch_kpi_trends,
        fetch_executive_summary,
        fetch_alerts_summary,
        fetch_ebitda_margin,
        fetch_value_leakage,
    )

    kpi = fetch_kpi_trends.invoke({"company_symbol": "CVS", "kpi_name": "ebitda-margin"})
    assert kpi["kpi"] == "ebitda-margin" and kpi["points"][0]["value"] == 1.0

    es = fetch_executive_summary.invoke({"company_symbol": "CVS"})
    assert es["highlights"] == ["H1"] and es["cta"] == "CTA"

    al = fetch_alerts_summary.invoke({"company_symbol": "CVS"})
    assert al["categories"] == ["c1"] and al["top_alerts"] == ["A1", "A2"]

    em = fetch_ebitda_margin.invoke({"company_symbol": "CVS"})
    assert em["trend"] == "up" and em["current_margin"] == 40.0

    vl = fetch_value_leakage.invoke({"company_symbol": "CVS"})
    assert vl["areas"] == ["Area1"] and vl["priority"] == "high"


def test_analyze_margin_node_enrichment(monkeypatch: pytest.MonkeyPatch):
    # Patch DI to return fake tools
    monkeypatch.setattr(di, "get_tools", lambda: _FakeTools())

    state = JoyceState(
        query="what's driving margin?",
        intent="margin_driver",
        context={"tenantId": "CVS", "threadId": "t1", "uiContext": {"module": "mirror"}},
        tools_to_call=[],
        evidence=[],
        response="",
        actions=[],
    )
    out = analyze_margin_node(state)
    ctx = out["context"]
    assert "mirror" in ctx and "alerts" in ctx
    assert "ebitda_margin" in ctx["mirror"]
    assert "value_leakage" in ctx["mirror"]
    analysis = ctx["analysis"]
    assert isinstance(analysis["drivers"], list)
    # Evidence docIds accumulated
    ev = out["evidence"]
    assert any(s.docId for s in ev)


def test_inproc_memory_store_roundtrip():
    from joyce_svc.adapters.memory.inproc import InprocMemoryStore
    from joyce_svc.ports.memory import MemoryTurn

    store = InprocMemoryStore(window=8, ttl_min=30)
    tid = "thread-xyz"

    store.save(thread_id=tid, turn=MemoryTurn(role="user", content="Hi"))
    store.save(thread_id=tid, turn=MemoryTurn(role="assistant", content="Hello"))
    snap = store.load(thread_id=tid)

    assert snap.thread_id == tid
    assert len(snap.turns) == 2
    assert snap.turns[0].role == "user"
    # Ensure trim works
    store.trim(thread_id=tid, window=1)
    snap2 = store.load(thread_id=tid)
    assert len(snap2.turns) <= 1
