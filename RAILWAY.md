# Deploying joyce-svc (advisor_v1 SSE) to Railway

This service exposes:
- GET /health (JSON) — liveness, version, uptime
- POST /v1/chat (SSE) — advisor_v1 streaming frames (start → chunk → final; error on auth failure)

It is designed to run behind the Node/Express proxy in the main app. The Node service proxies:
- POST /api/joyce/v1/chat → Python /v1/chat
- GET /api/joyce/health → Python /health

This document describes a production-ready setup on Railway, including environment variables, data mounting, health checks, and Node integration.

## 1) Prerequisites

- A Railway project connected to this GitHub repo (Railway will auto-detect Dockerfile).
- OIDC/JWT configuration (issuer, audience, JWKS URL or local JWKS file).
- A plan for mounting company data files (see “Data” section).

## 2) Build & Runtime on Railway

Railway will:
- Build using the Dockerfile at repo root.
- Set `PORT` automatically (injected at runtime).
- Run the container with the CMD/ENTRYPOINT from Dockerfile.

Image highlights:
- python:3.13-slim-bookworm base
- Non-root runtime user
- Minimal OS footprint

No special build-time configuration is required.

## 3) Environment Variables

Set these in the Railway service dashboard.

Required (core):
- LOG_LEVEL=info
- AUTH_REQUIRED=true
- OIDC_ISSUER=https://YOUR_ISSUER
- OIDC_AUDIENCE=joyce-audience
- OIDC_JWKS_URL=https://YOUR_ISSUER/.well-known/jwks.json
  - Or OIDC_JWKS_PATH=/app/jwks.json (if you provide a JWKS file via a secret volume)
- ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,https://app-dev.rejoyce.ai,https://app-stg.rejoyce.ai,https://app.rejoyce.ai
  - Optional: ALLOWED_ORIGIN_REGEX=^https://(app|app-stg|app-dev)\.rejoyce\.ai$

Service behavior and performance:
- ENABLE_UI_ACTIONS=true
- STREAM_FROM_GRAPH=false
- START_CHUNK_DELAY_MS=200
- REQUEST_TIMEOUT_S=6
- RATE_LIMIT_PER_MIN=60

Data:
- DATA_ROOT=/data

Memory (MVP in-proc):
- MEMORY_ENABLED=false
- MEMORY_WINDOW=8
- MEMORY_TTL_MIN=30

LLM classifier fallback (optional):
- JOYCE_LLM_CLASSIFIER=false
- INTENT_LLM_MODEL=claude-sonnet-4-20250514
- ANTHROPIC_API_KEY=… (only required if JOYCE_LLM_CLASSIFIER=true)

Metrics (optional placeholder):
- METRICS_ENABLED=false

Consultant persona (JOYCE-009) (optional; deterministic ON by default, LLM OFF)
- CONSULTANT_VOICE_ENABLED=true | false     # default: true (deterministic wrapper adds 5-section consultant structure)
- CONSULTANT_VOICE_LLM_ENABLED=false | true # default: false (enable only if you want LLM refinement in this env)
- CONSULTANT_VOICE_MODEL=claude-sonnet-4-20250514 | gpt-4o-mini | ...
- CONSULTANT_VOICE_TIMEOUT_MS=800
- ANTHROPIC_API_KEY=…   # required if CONSULTANT_VOICE_MODEL starts with "claude" (Anthropic SDK path)
- OPENAI_API_KEY=…      # required if using OpenAI/ChatOpenAI models (e.g., gpt-4o-mini)
Notes:
- When CONSULTANT_VOICE_MODEL is unset, it defaults to INTENT_LLM_MODEL.
- LLM refinement is timeboxed and falls back to deterministic wrapping on timeout/error.
- Keep CONSULTANT_VOICE_LLM_ENABLED=false in production if latency budgets are tight, or ensure adequate timeout and monitoring.

Notes:
- Do not set PORT manually; Railway injects it.
- Keep `.env` files for local dev only. All prod values live in Railway’s Environment tab.

## 4) Data (Mounting Company Files)

The service reads data from JSON files under `DATA_ROOT` (default /data):

- /data/companies/{symbol}/era-report/executive-summary-dashboard.json
- /data/companies/{symbol}/executive-summary.json
- /data/companies/{symbol}/alerts.json

Example content (minimal):

executive-summary-dashboard.json:
{
  "kpis": {
    "ebitda-margin": { "current_value": 42.1, "previous_value": 41.8, "period": "Q3 2025", "unit": "%" }
  },
  "financial_metrics": {
    "ebitda_margin": { "current_margin": 42.1, "previous_margin": 41.8, "period": "Q3 2025" }
  },
  "value_leakage": {
    "areas": ["Supply Chain Inefficiencies"],
    "estimated_impact": "2.3% margin improvement potential",
    "mitigation_strategies": ["Optimize supplier contracts"],
    "priority": "high"
  }
}

executive-summary.json:
{
  "key_highlights": ["Strong Q3 performance"],
  "risks": ["Labor inflation"],
  "recommendations": ["Accelerate digital health investments"],
  "overall_sentiment": "positive"
}

alerts.json:
{
  "alerts": {
    "high_priority": ["Supply chain optimization needed"],
    "operations": ["Traffic shift vs. historical"]
  }
}

How to mount on Railway:
- Add a Railway Volume and mount it at /data, then populate the directory structure above.
- Alternatively, bake static data into the image (COPY) for demo environments (not recommended for prod).
- Confirm DATA_ROOT=/data.

## 5) Health Check (Railway Settings)

Configure a health check:
- Path: /health
- Expect 200 JSON payload
- Interval: default is fine

## 6) Node/Express Proxy Integration

In the Node service:
- Configure:
  - JOYCE_ENABLED=true
  - JOYCE_SERVICE_URL=https://<your-python-service>.railway.app
- Proxy rules:
  - POST /api/joyce/v1/chat → {JOYCE_SERVICE_URL}/v1/chat
  - GET /api/joyce/health → {JOYCE_SERVICE_URL}/health
- For SSE:
  - Preserve `Content-Type: text/event-stream; charset=utf-8`
  - Do not buffer
- Forward headers:
  - Authorization: Bearer <jwt> (unaltered)
  - X-Correlation-Id (if present; can be generated by proxy if absent)
  - Accept: text/event-stream for /v1/chat
- Body:
  - Forward JSON body unmodified (advisor_v1 schema). Body is authoritative for tenantId/user; mismatches with JWT are logged as warnings in the Python service.

## 7) Security & Hardening

- Run as non-root in the container.
- Use Railway env vars and volumes for secrets (OIDC config, JWKS file).
- Keep image minimal; apply periodic rebuilds to receive base image security updates.
- Consider tighter CORS via ALLOWED_ORIGIN_REGEX in prod.

## 8) Verification (Smoke Tests)

Health:
curl -s "https://<python-service>.railway.app/health" | jq

SSE (DEV only, if AUTH_REQUIRED=false temporarily):
curl -N -H "Content-Type: application/json" \
  -H "X-Correlation-Id: 11111111-1111-4111-8111-111111111111" \
  -X POST "https://<python-service>.railway.app/v1/chat" \
  -d '{
    "tenantId":"CVS",
    "user":{"id":"u_123","role":"Investor"},
    "threadId":"th_789",
    "uiContext":{"module":"mirror","entityId":"kpi:ebitda-margin","route":"/digital-mirror?kpi=ebitda-margin"},
    "message":"Give me a 5-bullet investor brief for this month.",
    "response_format":"advisor_v1"
  }'

SSE with auth:
- Include Authorization: Bearer <jwt> with claims consistent with OIDC env vars.

## 9) Troubleshooting

- 401 on streaming: Check Authorization header and OIDC env vars. The /v1/chat route emits a single `type:error, content:"Unauthorized"` frame when auth fails.
- CORS errors: Ensure frontend origin is in ALLOWED_ORIGINS or matches ALLOWED_ORIGIN_REGEX.
- SSE not streaming: Ensure proxy does not buffer and preserves text/event-stream; charset=utf-8.
- Missing data in outputs: Verify /data directory structure and JSON keys exist under DATA_ROOT.
- “Module not found” locally: Start uvicorn with PYTHONPATH=src or `--app-dir src`.

## 10) Change Management

- All settings are controlled via Railway env vars (no .env files in prod).
- DATA_ROOT is volume-backed; coordinate data updates with your deployment process.
- The advisor_v1 contract is stable; do not change request/response field names without updating the proxy and UI.

## 11) Topology in Railway (same level as Rejoyce App)

Target layout (siblings in the same project):
- Rejoyce App (Node/Express) — proxies /api/joyce/* to this service
- rejoyce-joyce-agent (joyce-svc) — FastAPI SSE backend
- Postgres (optional; not required for MVP unless you enable DB-backed memory later)

Steps:
1) Create a new Railway service from this repo (not a Docker image service; Railway will build from the Dockerfile).
2) Ensure this Python service and the Rejoyce App are in the same Railway project (siblings).
3) In the Rejoyce App service, set:
   - JOYCE_ENABLED=true
   - JOYCE_SERVICE_URL=https://<python-service>.railway.app  (copy from this service’s “Domains” tab)
   - JOYCE_TIMEOUT (if used) to align with REQUEST_TIMEOUT_S (e.g., 6–15s)
4) In this Python service, set env vars as described in Section 3 (AUTH/CORS/DATA_ROOT, etc.).
5) If you assign custom domains to either service, update:
   - CORS in this Python service (ALLOWED_ORIGINS / ALLOWED_ORIGIN_REGEX) to include the Rejoyce App domain(s).
   - The Rejoyce App’s JOYCE_SERVICE_URL if the Python service domain changes.

Note about internal networking:
- For SSE stability and simplicity, prefer calling the public HTTPS domain of the Python service from the Rejoyce App (JOYCE_SERVICE_URL).
- Keep both services in the same project for unified management and logs; each service gets its own environment variables and deploys independently.

## 12) Deployment Checklist (dev/stg/prod)

- [ ] Python service deployed (green) with /health returning 200 JSON
- [ ] Volume mounted at /data with required JSON files (or demo data baked for non-prod)
- [ ] AUTH_REQUIRED set correctly for your environment (true in stg/prod)
- [ ] OIDC_ISSUER / OIDC_AUDIENCE / OIDC_JWKS_URL (or OIDC_JWKS_PATH) configured
- [ ] ALLOWED_ORIGINS (and ALLOWED_ORIGIN_REGEX if used) includes the Rejoyce App domain(s)
- [ ] Rejoyce App env:
      - [ ] JOYCE_ENABLED=true
      - [ ] JOYCE_SERVICE_URL=https://<python-service>.railway.app
- [ ] Railway health check configured for /health
- [ ] Manual smoke:
      - [ ] curl https://<python-service>.railway.app/health (200)
      - [ ] curl -N POST https://<python-service>.railway.app/v1/chat (SSE frames per advisor_v1; use auth or temporary AUTH_REQUIRED=false)
- [ ] End-to-end smoke via Node proxy:
      - [ ] curl https://<rejoyce-app>.railway.app/api/joyce/health (200 from Python)
      - [ ] SSE via /api/joyce/v1/chat streams (headers preserved: Accept: text/event-stream)

Tips:
- Concurrency: start with single worker; if you see CPU-bound load, set WEB_CONCURRENCY=2 or UVICORN_WORKERS=2. Keep in mind SSE holds connections; scale horizontally if needed.
- Timeouts: align JOYCE_TIMEOUT in Node with REQUEST_TIMEOUT_S in Python; leave room for network and proxy overhead.
- Observability: rely on correlation IDs (X-Correlation-Id) to trace across Node and Python logs.
