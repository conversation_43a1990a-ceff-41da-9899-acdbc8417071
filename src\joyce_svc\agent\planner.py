from __future__ import annotations

import json
import re
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from joyce_svc.config import settings
from joyce_svc.logging import get_logger

logger = get_logger(__name__)


_SUPPORTED_INTENTS = {"investor_snapshot", "margin_driver", "execution_proposal", "fallback"}


@dataclass
class PlanningResult:
    """Structured output from the LLM router."""

    intent: Optional[str]
    tools: List[str]
    reasoning: Optional[str] = None
    confidence: Optional[float] = None
    clarifying_question: Optional[str] = None


def _parse_float(value: Any) -> Optional[float]:
    try:
        if value is None:
            return None
        return float(value)
    except (TypeError, ValueError):
        return None


def _extract_json_block(text: str) -> Optional[Dict[str, Any]]:
    """Extract the first JSON object from the LLM output."""
    if not text:
        return None
    # Remove fenced code blocks if present
    fenced = re.search(r"```json\s*(\{.*?\})\s*```", text, flags=re.DOTALL)
    if fenced:
        try:
            return json.loads(fenced.group(1))
        except json.JSONDecodeError:
            pass
    # Fallback: grab the first {...} block
    bracket = re.search(r"\{.*\}", text, flags=re.DOTALL)
    if bracket:
        try:
            return json.loads(bracket.group(0))
        except json.JSONDecodeError:
            return None
    return None


def _call_openai_router(system_prompt: str, user_prompt: str) -> Optional[str]:
    from langchain_openai import ChatOpenAI  # type: ignore
    from langchain_core.messages import HumanMessage, SystemMessage  # type: ignore

    timeout = max(0.1, settings.AGENT_ROUTER_TIMEOUT_MS / 1000.0)
    model = ChatOpenAI(
        model=settings.AGENT_ROUTER_MODEL,
        temperature=0.0,
        timeout=timeout,
        max_retries=1,
    )
    result = model.invoke(
        [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt),
        ]
    )
    content = getattr(result, "content", None)
    if isinstance(content, str):
        return content
    if isinstance(content, list):
        return " ".join(str(part) for part in content)
    return str(result)


def _call_claude_router(system_prompt: str, user_prompt: str) -> Optional[str]:
    import os
    from anthropic import Anthropic  # type: ignore

    api_key = settings.ANTHROPIC_API_KEY or os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        raise ValueError("ANTHROPIC_API_KEY not provided for router model")

    timeout = max(0.1, settings.AGENT_ROUTER_TIMEOUT_MS / 1000.0)
    client = Anthropic(api_key=api_key, max_retries=1)
    response = client.messages.create(
        model=settings.AGENT_ROUTER_MODEL,
        max_tokens=400,
        temperature=0.0,
        timeout=timeout,
        system=system_prompt,
        messages=[{"role": "user", "content": user_prompt}],
    )
    if response.content:
        return response.content[0].text
    return None


def _call_router_llm(system_prompt: str, user_prompt: str) -> Optional[str]:
    model_name = (settings.AGENT_ROUTER_MODEL or "").lower()
    if "claude" in model_name:
        return _call_claude_router(system_prompt, user_prompt)
    return _call_openai_router(system_prompt, user_prompt)


def plan_with_llm(query: str, *, ctx: Dict[str, Any]) -> Optional[PlanningResult]:
    """
    Attempt to plan via LLM. Returns None when disabled or on failure.
    """
    if not settings.AGENT_LLM_ROUTER_ENABLED:
        return None
    prompt_ctx = {
        "query": query or "",
        "ui": ctx.get("uiContext", {}),
        "route": ctx.get("uiContext", {}).get("route"),
        "module": ctx.get("uiContext", {}).get("module"),
    }

    system_prompt = (
        "You are Joyce's deterministic planning assistant. "
        "Choose the best matching flow intent and the minimal set of tools to call. "
        "Supported intents: investor_snapshot, margin_driver, execution_proposal, fallback. "
        "Supported tools: fetch_mirror_data, fetch_sei_data, fetch_alerts_data, analyze_margin. "
        "Return strict JSON with keys intent, tools (array), reasoning, confidence (0-1), and optional clarifying_question."
    )
    user_prompt = (
        "User query: {query}\n"
        "UI context: {ui}\n"
        "Route hint: {route}\n"
        "Module: {module}\n"
        "Respond with JSON only."
    ).format(**prompt_ctx)

    try:
        raw = _call_router_llm(system_prompt, user_prompt)
    except Exception:
        logger.debug("LLM router call failed", exc_info=True)
        return None

    data = _extract_json_block(raw or "")
    if not data:
        logger.debug("Router LLM returned unparseable payload", extra={"raw": raw})
        return None

    intent = str(data.get("intent") or "").strip().lower() or None
    if intent and intent not in _SUPPORTED_INTENTS:
        logger.debug("Router LLM suggested unsupported intent", extra={"intent": intent})
        intent = None

    tools_raw = data.get("tools") or []
    tools: List[str] = []
    if isinstance(tools_raw, list):
        for item in tools_raw:
            if not isinstance(item, str):
                continue
            name = item.strip()
            if name:
                tools.append(name)

    reasoning = data.get("reasoning")
    confidence = _parse_float(data.get("confidence"))
    clarifying = data.get("clarifying_question")
    if isinstance(clarifying, str) and clarifying.strip():
        clarifying = clarifying.strip()
    else:
        clarifying = None

    return PlanningResult(
        intent=intent,
        tools=tools,
        reasoning=str(reasoning) if reasoning else None,
        confidence=confidence,
        clarifying_question=clarifying,
    )
