from __future__ import annotations

from typing import Any, Dict, List
from datetime import datetime, timedelta

from joyce_svc.adapters.links.basic import DeepLinkBuilder
from joyce_svc.models.flows import ExecutionProposalModel
from joyce_svc.models.schemas import Action, SourceRef


def _safe_str_items(items, limit: int = 5) -> List[str]:
    """Extract string items safely with limit."""
    out: List[str] = []
    if isinstance(items, list):
        for it in items:
            if isinstance(it, (str, int, float)):
                out.append(str(it))
            if len(out) >= limit:
                break
    return out


def _extract_owners_by_initiative(ctx: Dict[str, Any]) -> Dict[str, str]:
    """Extract owner mappings from dependencies and strategic initiatives."""
    roadmap = ctx.get("execution_roadmap", {})
    dependencies = roadmap.get("dependencies", [])
    
    # Create owner mapping from dependencies
    owner_map = {}
    for dep in dependencies:
        if isinstance(dep, dict):
            name = dep.get("name", "")
            owner = dep.get("owner", "")
            if name and owner:
                owner_map[name] = owner
    
    # Map strategic initiatives to functional owners based on content
    initiatives = roadmap.get("strategicInitiativeCards", [])
    initiative_owners = {}
    
    for initiative in initiatives:
        if isinstance(initiative, dict):
            title = initiative.get("title", "")
            description = initiative.get("description", "").lower()
            
            # Simple heuristic mapping based on description content
            if "cloud" in description or "platform" in description:
                initiative_owners[title] = "Technology Innovation"
            elif "ai" in description or "data" in description:
                initiative_owners[title] = "Technology Innovation"
            elif "industry" in description or "manufacturing" in description:
                initiative_owners[title] = "Industry Practices"
            elif "sustainability" in description or "esg" in description:
                initiative_owners[title] = "Industry Practices"
            else:
                initiative_owners[title] = "Alliance Management"
    
    return initiative_owners


def _structure_timeline_view(ctx: Dict[str, Any]) -> Dict[str, List[str]]:
    """Structure timeline into 30/60/90 day view from phasedExecutionPlan."""
    roadmap = ctx.get("execution_roadmap", {})
    phased_plan = roadmap.get("phasedExecutionPlan", {})
    implementation_timeline = roadmap.get("implementationTimeline", [])
    
    timeline_view = {
        "30_day": [],
        "60_day": [],
        "90_day": []
    }
    
    # Extract current and next quarters from implementation timeline
    current_actions = []
    next_actions = []
    future_actions = []
    
    for phase in implementation_timeline:
        if isinstance(phase, dict):
            quarter = phase.get("quarter", "")
            phase_name = phase.get("phase", "")
            items = phase.get("items", [])
            
            if "Q3 2024" in quarter or "Q4 2024" in quarter:
                current_actions.extend(_safe_str_items(items, limit=2))
            elif "Q1 2025" in quarter:
                next_actions.extend(_safe_str_items(items, limit=2))
            elif "Q2 2025" in quarter:
                future_actions.extend(_safe_str_items(items, limit=2))
    
    # Extract specific tasks from phasedExecutionPlan
    for initiative, phases in phased_plan.items():
        if isinstance(phases, list):
            for phase in phases:
                if isinstance(phase, dict):
                    quarter = phase.get("quarter", "")
                    task = phase.get("task", "")
                    investment = phase.get("investment", "")
                    
                    if task:
                        task_desc = f"{task}"
                        if investment:
                            task_desc += f" (Investment: {investment})"
                        
                        if "Q3 2024" in quarter or "Q4 2024" in quarter:
                            timeline_view["30_day"].append(task_desc)
                        elif "Q1 2025" in quarter:
                            timeline_view["60_day"].append(task_desc)
                        elif "Q2 2025" in quarter:
                            timeline_view["90_day"].append(task_desc)
    
    # Add general timeline items if specific tasks not available
    if not timeline_view["30_day"]:
        timeline_view["30_day"] = current_actions[:3]
    if not timeline_view["60_day"]:
        timeline_view["60_day"] = next_actions[:3]
    if not timeline_view["90_day"]:
        timeline_view["90_day"] = future_actions[:3]
    
    return timeline_view


def _get_initiative_count_and_evidence(ctx: Dict[str, Any]) -> tuple[int, List[str]]:
    """Extract initiative count and financial evidence."""
    roadmap = ctx.get("execution_roadmap", {})
    initiatives = roadmap.get("strategicInitiativeCards", [])
    size_of_prize = roadmap.get("sizeOfPrize", [])
    
    initiative_count = len(initiatives) if isinstance(initiatives, list) else 0
    
    evidence = []
    for prize in size_of_prize:
        if isinstance(prize, dict):
            initiative = prize.get("initiative", "")
            impact = prize.get("financialImpact", "")
            horizon = prize.get("timeHorizon", "")
            if initiative and impact:
                evidence.append(f"{initiative}: {impact} over {horizon}")
    
    return initiative_count, evidence[:3]


def compose_execution_proposal(ctx: Dict[str, Any], sources: List[SourceRef]) -> ExecutionProposalModel:
    """
    Compose execution proposal with owners and timelines mapped from execution-roadmap.json.
    Identifies existing initiatives, maps to function-level owners, extracts timelines from
    phasedExecutionPlan, and structures 30/60/90 day view with evidence.
    """
    safe_ctx = ctx or {}
    
    # Extract data from execution roadmap
    initiative_owners = _extract_owners_by_initiative(safe_ctx)
    timeline_view = _structure_timeline_view(safe_ctx)
    initiative_count, evidence = _get_initiative_count_and_evidence(safe_ctx)
    
    # Build content sections
    lines: List[str] = []
    
    # Summary
    owner_count = len(set(initiative_owners.values())) if initiative_owners else 0
    lines.append(f"Execution proposal: {initiative_count} strategic initiatives mapped to {owner_count} functional owners.")
    
    # Owners mapping
    if initiative_owners:
        owner_assignments = []
        for initiative, owner in initiative_owners.items():
            owner_assignments.append(f"{owner}: {initiative}")
        lines.append(f"Owner assignments: {'; '.join(owner_assignments[:4])}.")
    
    # Timeline view
    timeline_parts = []
    if timeline_view["30_day"]:
        timeline_parts.append(f"30-day: {'; '.join(timeline_view['30_day'][:2])}")
    if timeline_view["60_day"]:
        timeline_parts.append(f"60-day: {'; '.join(timeline_view['60_day'][:2])}")
    if timeline_view["90_day"]:
        timeline_parts.append(f"90-day: {'; '.join(timeline_view['90_day'][:2])}")
    
    if timeline_parts:
        lines.append(f"Timelines: {'; '.join(timeline_parts)}.")
    
    # Evidence
    if evidence:
        lines.append(f"Financial evidence: {'; '.join(evidence[:2])}.")
    
    content = " ".join(lines).strip()
    
    # UI Actions with deep links
    ui = safe_ctx.get("uiContext", {})
    links = DeepLinkBuilder(ui_context=ui)
    actions = [
        Action(label="View Execution Roadmap", href=links.execution_roadmap()),
        Action(label="View Recommendations", href=links.recommendations_roadmap())
    ]
    
    return ExecutionProposalModel(content=content, actions=actions)
