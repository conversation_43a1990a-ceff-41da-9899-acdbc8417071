from __future__ import annotations

import time
from fastapi import APIRouter

from joyce_svc import __version__

router = APIRouter()

# Record process start to compute uptime
_PROCESS_START = time.time()


@router.get("/health", tags=["system"])
async def health():
    """
    Liveness probe.
    Returns:
      {
        "status": "ok",
        "version": "<semver>",
        "uptimeSec": <number>
      }
    """
    uptime = max(0, int(time.time() - _PROCESS_START))
    return {"status": "ok", "version": __version__, "uptimeSec": uptime}
