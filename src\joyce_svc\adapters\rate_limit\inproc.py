from __future__ import annotations

import time
from collections import defaultdict, deque
from typing import Deque, Dict

from joyce_svc.ports.rate_limit import RateLimiterPort


class InProcRateLimiter(RateLimiterPort):
    """
    In-process per-key rate limiter.
    - Window: 60 seconds (fixed)
    - Limit: limit_per_min requests per window
    NOTE: Per-process only; resets on restart. Suitable for MVP/dev.
    """

    WINDOW_SEC = 60

    def __init__(self, *, limit_per_min: int = 60) -> None:
        self._limit = int(limit_per_min)
        self._requests: Dict[str, Deque[float]] = defaultdict(deque)

    def allow(self, *, key: str) -> bool:
        now = time.time()
        q = self._requests[key]
        # Drop old entries
        cutoff = now - self.WINDOW_SEC
        while q and q[0] < cutoff:
            q.popleft()
        if len(q) >= self._limit:
            return False
        q.append(now)
        return True
