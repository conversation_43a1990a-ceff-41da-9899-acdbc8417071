from __future__ import annotations

from joyce_svc.ports.tools import (
    ToolsPort,
    KPITrendModel,
    ExecutiveSummaryModel,
    AlertsSummaryModel,
)


class InMemoryToolsAdapter(ToolsPort):
    """
    In-memory stub implementation of ToolsPort that mirrors previous static data.
    Returns validated Pydantic models.
    """

    def fetch_kpi_trends(self, *, company_symbol: str, kpi_name: str) -> KPITrendModel:
        # Deterministic payload used previously
        return KPITrendModel(
            kpi=kpi_name,
            points=[
                {"date": "2025-06", "value": 41.2},
                {"date": "2025-07", "value": 41.8},
                {"date": "2025-08", "value": 42.1},
            ],
        )

    def fetch_executive_summary(self, *, company_symbol: str) -> ExecutiveSummaryModel:
        return ExecutiveSummaryModel(
            highlights=[
                "Solid top-line growth vs. prior month.",
                "Margin expansion driven by mix and efficiency.",
            ],
            risks=["Rising input costs in select categories."],
            cta="Review Executive Overview for deeper context.",
        )

    def fetch_alerts_summary(self, *, company_symbol: str) -> AlertsSummaryModel:
        return AlertsSummaryModel(
            categories=["margin", "operations"],
            top_alerts=["Value leakage detected in Category X", "Unusual variance in Region Y"],
        )
