#!/usr/bin/env sh
set -e

# Defaults if not provided
: "${PORT:=8080}"
: "${LOG_LEVEL:=info}"
# Prefer WEB_CONCURRENCY (Railway/Heroku style), fallback to UVICORN_WORKERS, default 1
: "${WEB_CONCURRENCY:=}"
: "${UVICORN_WORKERS:=}"
WORKERS="${WEB_CONCURRENCY:-$UVICORN_WORKERS}"
if [ -z "$WORKERS" ]; then
  WORKERS=1
fi

# Ensure application sources are importable as 'joyce_svc'
export PYTHONPATH="/app/src:${PYTHONPATH:-}"

exec uvicorn joyce_svc.main:app \
  --host 0.0.0.0 \
  --port "$PORT" \
  --proxy-headers \
  --log-level "$LOG_LEVEL" \
  --forwarded-allow-ips "*" \
  --workers "$WORKERS"
