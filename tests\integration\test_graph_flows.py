"""Integration tests for LangGraph flows."""

import pytest
from joyce_svc.agent.graph import get_graph_app
from joyce_svc import di
from joyce_svc.evidence.doc_registry import DOC_SEI_EXEC_SUM, DOC_MIRROR_TRENDS, DOC_ALERTS


class FakeToolsForGraphTests:
    """Fake tools for predictable graph testing."""

    def fetch_kpi_trends(self, *, company_symbol: str, kpi_name: str):
        from joyce_svc.ports.tools import KPITrendModel

        return KPITrendModel(
            kpi=kpi_name,
            points=[
                {"date": "2025-06", "value": 41.2},
                {"date": "2025-07", "value": 41.8},
                {"date": "2025-08", "value": 42.1},
            ],
        )

    def fetch_executive_summary(self, *, company_symbol: str):
        from joyce_svc.ports.tools import ExecutiveSummaryModel

        return ExecutiveSummaryModel(
            highlights=["Q3 revenue up 5%", "Market share gains"],
            risks=["Supply chain pressures"],
            cta="Review detailed metrics",
        )

    def fetch_alerts_summary(self, *, company_symbol: str):
        from joyce_svc.ports.tools import AlertsSummaryModel

        return AlertsSummaryModel(
            categories=["margin", "operations"],
            top_alerts=["Margin compression in Region A", "Inventory buildup"],
        )

    def fetch_ebitda_margin(self, *, company_symbol: str):
        from joyce_svc.ports.tools import EBITDAMarginModel

        return EBITDAMarginModel(
            current_margin=42.1, previous_margin=41.8, trend="up", period="Q3 2025"
        )

    def fetch_value_leakage(self, *, company_symbol: str):
        from joyce_svc.ports.tools import ValueLeakageModel

        return ValueLeakageModel(
            areas=["Procurement inefficiencies"],
            estimated_impact="1.5% margin opportunity",
            mitigation_strategies=["Renegotiate contracts"],
            priority="high",
        )


def _fresh_graph():
    if hasattr(get_graph_app, "cache_clear"):
        get_graph_app.cache_clear()
    return get_graph_app()


def test_investor_snapshot_flow_integration(monkeypatch):
    """Test full investor snapshot flow through graph."""
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForGraphTests())
    graph = _fresh_graph()

    initial_state = {
        "query": "Give me a 5-bullet investor brief",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": "test-thread-1",
            "uiContext": {"module": "mirror"},
            "user": {"id": "u1", "role": "investor"},
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": [],
    }

    final_state = graph.invoke(initial_state)

    # Verify intent was classified
    assert final_state.intent == "investor_snapshot"

    # Verify response was generated with 5 numbered bullets
    assert isinstance(final_state.response, str) and len(final_state.response) > 20
    lines = final_state.response.splitlines()
    numbered_lines = [line for line in lines if line.strip() and line.strip()[0].isdigit()]
    assert len(numbered_lines) == 5, f"Expected 5 numbered bullets, got {len(numbered_lines)}"

    # Verify EBITDA proxy disclosure is present
    assert "EBITDA margin" in final_state.response
    assert "proxied" in final_state.response or "proxy" in final_state.response

    # Verify context contains required data
    ctx = final_state.context
    assert "mirror" in ctx
    assert "trends" in ctx["mirror"]
    assert "ebitda_margin" in ctx["mirror"]
    assert "sei" in ctx
    assert "alerts" in ctx

    # Verify evidence was collected from all three sources
    assert len(final_state.evidence) > 0
    doc_ids = {e.docId for e in final_state.evidence}
    assert DOC_MIRROR_TRENDS in doc_ids
    assert DOC_SEI_EXEC_SUM in doc_ids
    assert DOC_ALERTS in doc_ids

    # Verify actions were generated with correct deep link
    assert len(final_state.actions) >= 1
    assert final_state.actions[0].label == "Open Executive Overview"
    assert final_state.actions[0].href == "/sei-report/executive-summary"


def test_margin_driver_flow_integration(monkeypatch):
    """Test full margin driver analysis flow through graph."""
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForGraphTests())
    graph = _fresh_graph()

    initial_state = {
        "query": "What's driving the gross margin change?",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": "test-thread-2",
            "uiContext": {"module": "mirror", "entityId": "kpi:ebitda-margin"},
            "user": {"id": "u2", "role": "analyst"},
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": [],
    }

    final_state = graph.invoke(initial_state)

    # Verify intent
    assert final_state.intent == "margin_driver"

    # Verify margin analysis was performed
    ctx = final_state.context
    assert "mirror" in ctx
    assert "ebitda_margin" in ctx["mirror"]
    assert "value_leakage" in ctx["mirror"]
    assert "alerts" in ctx

    # Verify evidence
    assert len(final_state.evidence) > 0
    doc_ids = {e.docId for e in final_state.evidence}
    assert DOC_MIRROR_TRENDS in doc_ids or DOC_ALERTS in doc_ids


def test_execution_proposal_flow_integration(monkeypatch):
    """Test execution proposal flow through graph."""
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForGraphTests())
    graph = _fresh_graph()

    initial_state = {
        "query": "Propose owners and timelines for margin improvement",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": "test-thread-3",
            "uiContext": {"module": "sei"},
            "user": {"id": "u3", "role": "executive"},
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": [],
    }

    final_state = graph.invoke(initial_state)

    # Verify intent
    assert final_state.intent == "execution_proposal"

    # Verify response structure contains expected cues
    response = final_state.response
    assert isinstance(response, str) and (("owner" in response.lower()) or ("timeline" in response.lower()))


def test_unclear_intent_clarification(monkeypatch):
    """Test that unclear intent triggers clarification."""
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForGraphTests())
    graph = _fresh_graph()

    initial_state = {
        "query": "Hello, what can you do?",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": "test-thread-4",
            "uiContext": {"module": "mirror"},
            "user": {"id": "u4", "role": "viewer"},
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": [],
    }

    final_state = graph.invoke(initial_state)

    # Should not have a specific intent
    assert final_state.intent is None

    # Should have clarifying response (mention possible flows)
    response = final_state.response.lower()
    assert ("investor" in response) or ("margin" in response) or ("execution" in response)


def test_memory_persistence_across_invocations(monkeypatch):
    """Test memory persistence when MEMORY_ENABLED=True."""
    from joyce_svc.config import settings as svc_settings

    # Enable memory
    monkeypatch.setattr(svc_settings, "MEMORY_ENABLED", True)
    monkeypatch.setattr(di, "get_tools", lambda: FakeToolsForGraphTests())

    # Clear caches
    if hasattr(get_graph_app, "cache_clear"):
        get_graph_app.cache_clear()
    if hasattr(di.get_memory, "cache_clear"):
        di.get_memory.cache_clear()

    graph = get_graph_app()
    thread_id = "memory-test-thread"

    # First invocation
    state1 = {
        "query": "Give me an investor brief",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": thread_id,
            "uiContext": {"module": "mirror"},
            "user": {"id": "u5", "role": "investor"},
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": [],
    }

    final1 = graph.invoke(state1)
    assert final1.intent == "investor_snapshot"

    # Second invocation with same thread
    state2 = {
        "query": "What about margin drivers?",
        "intent": None,
        "context": {
            "tenantId": "CVS",
            "threadId": thread_id,
            "uiContext": {"module": "mirror"},
            "user": {"id": "u5", "role": "investor"},
        },
        "tools_to_call": [],
        "evidence": [],
        "response": "",
        "actions": [],
    }

    final2 = graph.invoke(state2)

    # Memory should have loaded previous context (behavior depends on implementation)
    assert final2.intent == "margin_driver"

    # Both queries should be in memory now
    memory = di.get_memory()
    snapshot = memory.load(thread_id=thread_id)
    assert len(snapshot.turns) >= 2
