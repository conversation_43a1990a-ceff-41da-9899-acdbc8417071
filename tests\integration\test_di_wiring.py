"""Integration tests for dependency injection wiring."""

import pytest
from joyce_svc import di
from joyce_svc.config import settings
from joyce_svc.ports.llm import IntentClassifier
from joyce_svc.adapters.tools.file_backed import FileBackedToolsAdapter
from joyce_svc.adapters.memory.inproc import InprocMemoryStore
from joyce_svc.adapters.llm.rule_based import RuleBasedClassifier
from joyce_svc.adapters.llm.ensemble import EnsembleClassifier


def test_di_singleton_behavior():
    """DI functions should return singletons via @lru_cache."""
    # Clear caches first
    if hasattr(di.get_tools, "cache_clear"):
        di.get_tools.cache_clear()
    if hasattr(di.get_memory, "cache_clear"):
        di.get_memory.cache_clear()
    if hasattr(di.get_intent_classifier, "cache_clear"):
        di.get_intent_classifier.cache_clear()
    if hasattr(di.get_rate_limiter, "cache_clear"):
        di.get_rate_limiter.cache_clear()
    
    # Get instances
    tools1 = di.get_tools()
    tools2 = di.get_tools()
    
    memory1 = di.get_memory()
    memory2 = di.get_memory()
    
    classifier1 = di.get_intent_classifier()
    classifier2 = di.get_intent_classifier()
    
    limiter1 = di.get_rate_limiter()
    limiter2 = di.get_rate_limiter()
    
    # Verify singletons
    assert tools1 is tools2
    assert memory1 is memory2
    assert classifier1 is classifier2
    assert limiter1 is limiter2


def test_tools_adapter_selection(monkeypatch):
    """DI should select FileBackedToolsAdapter when DATA_ROOT is set."""
    # Clear cache
    if hasattr(di.get_tools, "cache_clear"):
        di.get_tools.cache_clear()
    
    # Set DATA_ROOT
    monkeypatch.setattr(settings, "DATA_ROOT", "/test/data")
    
    tools = di.get_tools()
    
    # Should be FileBackedToolsAdapter
    assert isinstance(tools, FileBackedToolsAdapter)
    # Duck-typing check (avoid Protocol isinstance checks on Py3.13)
    assert hasattr(tools, "fetch_kpi_trends")
    assert hasattr(tools, "fetch_executive_summary")


def test_memory_adapter_selection():
    """DI should return InprocMemoryStore."""
    # Clear cache
    if hasattr(di.get_memory, "cache_clear"):
        di.get_memory.cache_clear()
    
    memory = di.get_memory()
    
    # Should be InprocMemoryStore
    assert isinstance(memory, InprocMemoryStore)
    # Duck-typing check
    assert hasattr(memory, "load") and hasattr(memory, "save")
    
    # Verify configuration
    assert memory._window == settings.MEMORY_WINDOW
    # TTL is tracked in seconds internally
    assert memory._ttl_sec == settings.MEMORY_TTL_MIN * 60


def test_intent_classifier_selection_rule_based(monkeypatch):
    """DI should return RuleBasedClassifier when LLM is disabled."""
    # Clear cache
    if hasattr(di.get_intent_classifier, "cache_clear"):
        di.get_intent_classifier.cache_clear()
    
    # Disable LLM classifier
    monkeypatch.setattr(settings, "JOYCE_LLM_CLASSIFIER", False)
    
    classifier = di.get_intent_classifier()
    
    # Should be RuleBasedClassifier
    assert isinstance(classifier, RuleBasedClassifier)
    # Duck-typing check
    assert hasattr(classifier, "classify")


def test_intent_classifier_selection_ensemble(monkeypatch):
    """DI should return EnsembleClassifier when LLM is enabled."""
    # Clear cache
    if hasattr(di.get_intent_classifier, "cache_clear"):
        di.get_intent_classifier.cache_clear()
    
    # Enable LLM classifier
    monkeypatch.setattr(settings, "JOYCE_LLM_CLASSIFIER", True)
    
    classifier = di.get_intent_classifier()
    
    # Should be EnsembleClassifier
    assert isinstance(classifier, EnsembleClassifier)
    # Duck-typing check
    assert hasattr(classifier, "classify")
    
    # Verify it has primary and fallback
    assert classifier._primary is not None
    assert classifier._fallback is not None


def test_rate_limiter_configuration():
    """DI should configure rate limiter with settings."""
    # Clear cache
    if hasattr(di.get_rate_limiter, "cache_clear"):
        di.get_rate_limiter.cache_clear()
    
    limiter = di.get_rate_limiter()
    
    # Should expose allow(key=...) (duck-typing)
    assert hasattr(limiter, "allow")
    
    # Verify configuration
    assert limiter._limit == settings.RATE_LIMIT_PER_MIN


def test_cache_clearing_changes_instances():
    """Clearing cache should allow new instances."""
    # Get initial instance
    tools1 = di.get_tools()
    
    # Clear cache
    if hasattr(di.get_tools, "cache_clear"):
        di.get_tools.cache_clear()
    
    # Get new instance
    tools2 = di.get_tools()
    
    # Should be different instances
    assert tools1 is not tools2
    
    # But subsequent calls should return same
    tools3 = di.get_tools()
    assert tools2 is tools3


def test_data_root_change_requires_cache_clear(monkeypatch, tmp_path):
    """Changing DATA_ROOT should require cache clear for new adapter."""
    # Clear cache
    if hasattr(di.get_tools, "cache_clear"):
        di.get_tools.cache_clear()
    
    # Set initial DATA_ROOT
    data_root1 = tmp_path / "data1"
    data_root1.mkdir()
    monkeypatch.setattr(settings, "DATA_ROOT", str(data_root1))
    
    tools1 = di.get_tools()
    assert str(tools1.data_root) == str(data_root1)
    
    # Change DATA_ROOT without clearing cache
    data_root2 = tmp_path / "data2"
    data_root2.mkdir()
    monkeypatch.setattr(settings, "DATA_ROOT", str(data_root2))
    
    # Should still get old instance (cached)
    tools2 = di.get_tools()
    assert str(tools2.data_root) == str(data_root1)  # Still old path
    
    # Clear cache and get new instance
    di.get_tools.cache_clear()
    tools3 = di.get_tools()
    assert str(tools3.data_root) == str(data_root2)  # New path


def test_metrics_adapter_noop():
    """DI should return NoopMetricsAdapter."""
    # Clear cache
    if hasattr(di.get_metrics, "cache_clear"):
        di.get_metrics.cache_clear()
    
    metrics = di.get_metrics()
    
    # Should be noop
    from joyce_svc.adapters.metrics.noop import NoopMetrics
    assert isinstance(metrics, NoopMetrics)
    
    # Should not raise on operations (noop methods)
    metrics.inc_auth_failures()
    metrics.inc_rate_limit_hits()
    metrics.observe_request_duration_ms(path="/test", method="GET", status_code=200, elapsed_ms=1.23)
    metrics.observe_node_duration_ms(node="classify_intent", elapsed_ms=0.5)
    metrics.set_gauge(name="test_gauge", value=42)
