from __future__ import annotations

from typing import Protocol, Optional


class MetricsPort(Protocol):
    """
    Minimal metrics port for counters and histograms.
    Implementations may be no-op, OTLP, Prometheus, etc.
    """

    def inc_auth_failures(self) -> None: ...
    def inc_rate_limit_hits(self) -> None: ...
    def observe_request_duration_ms(self, *, path: str, method: str, status_code: int, elapsed_ms: float) -> None: ...
    def observe_node_duration_ms(self, *, node: str, elapsed_ms: float) -> None: ...
    def set_gauge(self, *, name: str, value: float, labels: Optional[dict] = None) -> None: ...
