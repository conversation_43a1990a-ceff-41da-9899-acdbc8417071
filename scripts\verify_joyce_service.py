#!/usr/bin/env python3
"""Smoke-test Joyce service endpoints.

Usage:
   Start the Server:
    AUTH_REQUIRED=false DATA_ROOT=dev-data \venv/bin/uvicorn --app-dir src joyce_svc.main:app --host 127.0.0.1 --port 8080
   Run this script
    python scripts/verify_joyce_service.py --base-url http://127.0.0.1:8080
   
The script checks `/health` and then streams `/v1/chat` for three
representative prompts, printing frames as they arrive and summarising the
final response.
"""

from __future__ import annotations

import argparse
import json
import sys
from typing import Dict, Iterable, List

import httpx


PROMPTS = {
    "investor_snapshot": "Give me a 5-bullet investor brief for CVS",
    "margin_driver": "What's driving the gross margin change?",
    "execution_proposal": "Propose owners and timelines to address margin pressure",
}


def check_health(client: httpx.Client) -> None:
    resp = client.get("/health")
    resp.raise_for_status()
    print("/health →", resp.status_code, resp.json())


def _stream_sse(resp: httpx.Response) -> Iterable[Dict]:
    """Yield parsed SSE frames from an HTTPX streaming response."""
    for line in resp.iter_lines():
        if not line or not line.startswith("data: "):
            continue
        payload = line.replace("data: ", "", 1)
        try:
            yield json.loads(payload)
        except json.JSONDecodeError:
            print("[warn] failed to decode frame:", payload)


def run_chat(client: httpx.Client, intent: str, prompt: str) -> None:
    print(f"\n>>> {intent}: {prompt}")
    body = {
        "tenantId": "CVS",
        "user": {"id": "smoke-test", "role": "analyst"},
        "threadId": f"smoke-{intent}",
        "uiContext": {"module": "mirror"},
        "message": prompt,
        "response_format": "advisor_v1",
    }
    headers = {"Accept": "text/event-stream"}
    try:
        with client.stream("POST", "/v1/chat", json=body, headers=headers) as resp:
            resp.raise_for_status()
            frames: List[Dict] = []
            for frame in _stream_sse(resp):
                frames.append(frame)
                print("  frame", len(frames), "→", frame.get("type"), frame.get("content", "")[:80])
    except httpx.ReadTimeout:
        print("  [warn] stream timed out waiting for the next SSE event")
        return

    final = next((f for f in reversed(frames) if f.get("type") == "final"), None)
    if not final:
        print("[warn] no final frame returned")
        return
    actions = final.get("actions", []) or []
    sources = final.get("sources", []) or []
    print("  final summary:")
    print("    content: ", (final.get("content") or "").splitlines()[0][:120])
    print("    actions: ", len(actions))
    print("    sources: ", len(sources))


def main(argv: List[str]) -> int:
    parser = argparse.ArgumentParser(description="Verify Joyce service endpoints")
    parser.add_argument("--base-url", default="http://127.0.0.1:8080", help="Service base URL")
    args = parser.parse_args(argv)

    timeout = httpx.Timeout(40.0, read=40.0)
    with httpx.Client(base_url=args.base_url, timeout=timeout) as client:
        check_health(client)
        for intent, prompt in PROMPTS.items():
            run_chat(client, intent, prompt)

    print("\nAll checks complete.")
    return 0


if __name__ == "__main__":
    raise SystemExit(main(sys.argv[1:]))
